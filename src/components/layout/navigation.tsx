'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Menu } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { LanguageSwitcher } from '@/components/shared/language-switcher';
import { ThemeSwitcher } from '@/components/shared/theme-switcher';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

/**
 * 响应式导航栏组件
 * 包含品牌 Logo、导航菜单、语言切换器和主题切换器
 * 支持桌面端水平布局和移动端汉堡菜单
 */
export function Navigation(): React.JSX.Element {
  const t = useTranslations('navigation');
  const [isOpen, setIsOpen] = useState(false);

  const navigationItems = [
    { href: '/', label: t('home') },
    { href: '/products', label: t('products') },
    { href: '/blog', label: t('blog') },
    { href: '/about', label: t('about') },
  ];

  const closeSheet = (): void => setIsOpen(false);

  return (
    <nav className='bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 w-full border-b backdrop-blur'>
      <div className='container mx-auto flex h-16 items-center justify-between px-4'>
        {/* Logo */}
        <Link href='/' className='flex items-center space-x-2'>
          <div className='bg-primary flex h-8 w-8 items-center justify-center rounded-lg'>
            <span className='text-primary-foreground text-sm font-bold'>T</span>
          </div>
          <span className='text-xl font-bold'>Tucsenberg</span>
        </Link>

        {/* Desktop Navigation */}
        <div className='hidden items-center space-x-6 md:flex'>
          {navigationItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className='text-sm font-medium transition-colors hover:text-blue-600 dark:hover:text-blue-400'
            >
              {item.label}
            </Link>
          ))}
        </div>

        {/* Desktop Actions */}
        <div className='hidden items-center space-x-2 md:flex'>
          <LanguageSwitcher />
          <ThemeSwitcher />
        </div>

        {/* Mobile Menu */}
        <div className='md:hidden'>
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant='ghost' size='sm' className='p-2'>
                <Menu className='h-5 w-5' />
                <span className='sr-only'>{t('menu')}</span>
              </Button>
            </SheetTrigger>
            <SheetContent side='right' className='w-[300px] sm:w-[400px]'>
              <SheetHeader>
                <SheetTitle className='text-left'>
                  <div className='flex items-center space-x-2'>
                    <div className='bg-primary flex h-6 w-6 items-center justify-center rounded-lg'>
                      <span className='text-primary-foreground text-xs font-bold'>
                        T
                      </span>
                    </div>
                    <span className='font-bold'>Tucsenberg</span>
                  </div>
                </SheetTitle>
              </SheetHeader>

              <div className='mt-6 flex flex-col space-y-4'>
                {/* Mobile Navigation Links */}
                <div className='flex flex-col space-y-3'>
                  {navigationItems.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={closeSheet}
                      className='py-2 text-lg font-medium transition-colors hover:text-blue-600 dark:hover:text-blue-400'
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>

                {/* Mobile Actions */}
                <div className='border-t pt-4'>
                  <div className='flex flex-col space-y-3'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>
                        {t('language')}
                      </span>
                      <LanguageSwitcher />
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm font-medium'>{t('theme')}</span>
                      <ThemeSwitcher />
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}
