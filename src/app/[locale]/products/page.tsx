import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { UnderConstruction } from '@/components/content/under-construction';

/**
 * 产品页面
 *
 * 展示公司产品和服务的页面，目前使用建设中模板。
 * 支持多语言显示，配置了完整的SEO metadata。
 */

interface ProductsPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * 生成页面metadata
 */
export async function generateMetadata({
  params,
}: ProductsPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.products' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
  };
}

/**
 * 产品页面组件
 */
export default function ProductsPage({
  params: _params,
}: ProductsPageProps): React.JSX.Element {
  return <UnderConstruction />;
}
