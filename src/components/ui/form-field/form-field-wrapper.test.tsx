import { useForm } from 'react-hook-form';
import { describe, expect, it } from 'vitest';
import { Form } from '@/components/ui/form';
import { render, screen } from '@/test/utils';
import { FormFieldWrapper, RequiredIndicator } from './form-field-wrapper';

// 测试包装器组件
const TestWrapper = ({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement => {
  const form = useForm({
    defaultValues: {
      testField: '',
    },
  });

  return <Form {...form}>{children}</Form>;
};

// 测试辅助函数
const testBasicRender = (): void => {
  render(
    <TestWrapper>
      <FormFieldWrapper label='测试标签'>
        <input type='text' placeholder='测试输入' />
      </FormFieldWrapper>
    </TestWrapper>
  );

  expect(screen.getByText('测试标签')).toBeInTheDocument();
  expect(screen.getByPlaceholderText('测试输入')).toBeInTheDocument();
};

const testRequiredIndicator = (): void => {
  render(
    <TestWrapper>
      <FormFieldWrapper label='必填字段' required={true}>
        <input type='text' />
      </FormFieldWrapper>
    </TestWrapper>
  );

  const requiredIndicator = screen.getByLabelText('必填字段');
  expect(requiredIndicator).toBeInTheDocument();
  expect(requiredIndicator).toHaveClass('text-red-500');
};

const testOptionalField = (): void => {
  render(
    <TestWrapper>
      <FormFieldWrapper label='可选字段' required={false}>
        <input type='text' />
      </FormFieldWrapper>
    </TestWrapper>
  );

  expect(screen.queryByLabelText('必填字段')).not.toBeInTheDocument();
};

describe('FormFieldWrapper 组件', () => {
  it('应该正确渲染基本的表单字段包装器', () => {
    testBasicRender();
  });

  it('应该在必填时显示必填标识', () => {
    testRequiredIndicator();
  });

  it('应该在非必填时不显示必填标识', () => {
    testOptionalField();
  });

  it('应该在有错误时显示错误消息', () => {
    const errorMessage = '这是一个错误消息';
    render(
      <TestWrapper>
        <FormFieldWrapper label='测试字段' error={errorMessage}>
          <input type='text' />
        </FormFieldWrapper>
      </TestWrapper>
    );

    const errorElement = screen.getByRole('alert');
    expect(errorElement).toBeInTheDocument();
    expect(errorElement).toHaveTextContent(errorMessage);
  });

  it('应该在没有错误时不显示错误消息', () => {
    render(
      <TestWrapper>
        <FormFieldWrapper label='测试字段'>
          <input type='text' />
        </FormFieldWrapper>
      </TestWrapper>
    );

    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  it('应该在错误为空字符串时不显示错误消息', () => {
    render(
      <TestWrapper>
        <FormFieldWrapper label='测试字段' error=''>
          <input type='text' />
        </FormFieldWrapper>
      </TestWrapper>
    );

    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });

  it('应该正确设置 htmlFor 属性', () => {
    render(
      <TestWrapper>
        <FormFieldWrapper label='测试字段' htmlFor='test-input'>
          <input type='text' id='test-input' />
        </FormFieldWrapper>
      </TestWrapper>
    );

    const label = screen.getByText('测试字段');
    expect(label).toHaveAttribute('for', 'test-input');
  });

  it('应该正确渲染子组件', () => {
    render(
      <TestWrapper>
        <FormFieldWrapper label='测试字段'>
          <div data-testid='child-component'>子组件内容</div>
        </FormFieldWrapper>
      </TestWrapper>
    );

    expect(screen.getByTestId('child-component')).toBeInTheDocument();
    expect(screen.getByText('子组件内容')).toBeInTheDocument();
  });

  it('应该同时显示必填标识和错误消息', () => {
    render(
      <TestWrapper>
        <FormFieldWrapper label='必填字段' required={true} error='验证失败'>
          <input type='text' />
        </FormFieldWrapper>
      </TestWrapper>
    );

    expect(screen.getByLabelText('必填字段')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByText('验证失败')).toBeInTheDocument();
  });

  it('应该有正确的组件结构', () => {
    render(
      <TestWrapper>
        <FormFieldWrapper label='测试字段' required={true} error='错误消息'>
          <input type='text' data-testid='test-input' />
        </FormFieldWrapper>
      </TestWrapper>
    );

    // 检查 FormItem 容器
    const formItem = screen
      .getByTestId('test-input')
      .closest('[class*="space-y"]');
    expect(formItem).toBeInTheDocument();

    // 检查标签
    const label = screen.getByText('测试字段');
    expect(label).toBeInTheDocument();

    // 检查输入控件
    const input = screen.getByTestId('test-input');
    expect(input).toBeInTheDocument();

    // 检查错误消息
    const errorMessage = screen.getByRole('alert');
    expect(errorMessage).toBeInTheDocument();
  });
});

describe('RequiredIndicator 组件', () => {
  it('应该在必填时渲染必填标识', () => {
    render(<RequiredIndicator required={true} />);

    const indicator = screen.getByLabelText('必填字段');
    expect(indicator).toBeInTheDocument();
    expect(indicator).toHaveTextContent('*');
    expect(indicator).toHaveClass('text-red-500');
  });

  it('应该在非必填时不渲染', () => {
    render(<RequiredIndicator required={false} />);

    expect(screen.queryByLabelText('必填字段')).not.toBeInTheDocument();
  });

  it('应该在未指定 required 时不渲染', () => {
    render(<RequiredIndicator />);

    expect(screen.queryByLabelText('必填字段')).not.toBeInTheDocument();
  });

  it('应该支持自定义 aria-label', () => {
    render(<RequiredIndicator required={true} aria-label='自定义必填标识' />);

    const indicator = screen.getByLabelText('自定义必填标识');
    expect(indicator).toBeInTheDocument();
    expect(indicator).toHaveTextContent('*');
  });

  it('应该有正确的样式类', () => {
    render(<RequiredIndicator required={true} />);

    const indicator = screen.getByLabelText('必填字段');
    expect(indicator).toHaveClass('text-red-500');
    expect(indicator).toHaveClass('ml-1');
  });

  it('应该是内联元素', () => {
    render(
      <div>
        <span>标签文本</span>
        <RequiredIndicator required={true} />
      </div>
    );

    const indicator = screen.getByLabelText('必填字段');
    expect(indicator.tagName).toBe('SPAN');
  });
});
