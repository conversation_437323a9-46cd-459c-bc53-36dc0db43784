import { type ReactElement, memo } from 'react';
import { Button } from '@/components/ui/button';
import {
  type ErrorMessageProps,
  type FormSubmitButtonProps,
  type RetryButtonProps,
  type StatusMessageProps,
} from './contact-form-types';

/**
 * 成功状态消息组件
 */
export const SuccessMessage = memo(
  ({ memoizedT }: StatusMessageProps): ReactElement => {
    return (
      <div
        className='rounded-md bg-green-50 p-4 text-center text-sm text-green-800 dark:bg-green-900/20 dark:text-green-200'
        role='alert'
        aria-live='polite'
      >
        {memoizedT('success')}
      </div>
    );
  }
);
SuccessMessage.displayName = 'SuccessMessage';

/**
 * 错误消息组件
 */
export const ErrorMessage = memo(
  ({ submissionError }: ErrorMessageProps): ReactElement | null => {
    if (submissionError === null || submissionError === '') return null;

    return (
      <div
        className='rounded-md bg-red-50 p-4 text-center text-sm text-red-800 dark:bg-red-900/20 dark:text-red-200'
        role='alert'
        aria-live='assertive'
      >
        {submissionError}
      </div>
    );
  }
);
ErrorMessage.displayName = 'ErrorMessage';

/**
 * 重试按钮组件
 */
export const RetryButton = memo(
  ({
    canRetry,
    submissionError,
    handleRetry,
  }: RetryButtonProps): ReactElement | null => {
    if (!canRetry || submissionError === null || submissionError === '')
      return null;

    return (
      <Button
        type='button'
        variant='outline'
        size='sm'
        onClick={() => {
          void handleRetry();
        }}
        className='w-full'
      >
        重试提交
      </Button>
    );
  }
);
RetryButton.displayName = 'RetryButton';

/**
 * 表单提交按钮组件
 */
export const FormSubmitButton = memo(
  ({
    isSubmitting,
    isSubmittingWithRetry,
    memoizedT,
  }: FormSubmitButtonProps): ReactElement => {
    return (
      <Button
        type='submit'
        disabled={isSubmitting || isSubmittingWithRetry}
        className='w-full'
        size='lg'
      >
        {isSubmitting || isSubmittingWithRetry
          ? memoizedT('submitting')
          : memoizedT('submit')}
      </Button>
    );
  }
);
FormSubmitButton.displayName = 'FormSubmitButton';
