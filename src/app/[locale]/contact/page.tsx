import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { UnderConstruction } from '@/components/content/under-construction';

/**
 * 联系页面
 *
 * 展示联系方式和联系表单的页面，目前使用建设中模板。
 * 这是一个隐藏页面，不在导航菜单中显示，但可以通过直接 URL 访问。
 * 支持多语言显示，配置了完整的SEO metadata。
 * 为后续联系表单功能预留空间。
 */

interface ContactPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * 生成页面metadata
 */
export async function generateMetadata({
  params,
}: ContactPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.contact' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

/**
 * 联系页面组件
 *
 * 特性：
 * - 隐藏页面：不在导航菜单中显示
 * - 直接访问：可通过 /contact URL 访问
 * - 多语言支持：支持中英文切换
 * - SEO 优化：完整的 metadata 配置
 * - 建设中模板：为后续功能开发预留空间
 * - 响应式设计：适配所有设备
 */
export default function ContactPage({
  params: _params,
}: ContactPageProps): React.JSX.Element {
  return <UnderConstruction />;
}
