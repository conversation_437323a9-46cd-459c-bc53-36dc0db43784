'use client';

import Script from 'next/script';

/**
 * 主题脚本组件
 * 
 * 在页面加载前注入主题脚本，防止主题闪烁
 * 确保在 hydration 之前正确设置主题
 */
export function ThemeScript() {
  const themeScript = `
    (function() {
      function getThemePreference() {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }
      
      const theme = getThemePreference();
      
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
      
      // 设置 data-theme 属性用于 CSS 变量
      document.documentElement.setAttribute('data-theme', theme);
    })();
  `;

  return (
    <Script
      id="theme-script"
      strategy="beforeInteractive"
      dangerouslySetInnerHTML={{
        __html: themeScript,
      }}
    />
  );
}
