import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { UnderConstruction } from '@/components/content/under-construction';

/**
 * 博客页面
 *
 * 展示公司博客文章和技术分享的页面，目前使用建设中模板。
 * 支持多语言显示，配置了完整的SEO metadata。
 */

interface BlogPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * 生成页面metadata
 */
export async function generateMetadata({
  params,
}: BlogPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.blog' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
  };
}

/**
 * 博客页面组件
 */
export default function BlogPage({
  params: _params,
}: BlogPageProps): React.JSX.Element {
  return <UnderConstruction />;
}
