import { useCallback, useMemo, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useFormSubmission } from '@/hooks/use-form-submission';
import { type ContactFormData, contactFormSchema } from '@/lib/validations';
import {
  type ContactFormLogicReturn,
  type FormStatus,
} from './contact-form-types';

/**
 * 联系表单逻辑Hook
 *
 * 提供表单状态管理、验证和提交逻辑
 */
export const useContactFormLogic = (): ContactFormLogicReturn => {
  // 提交状态管理
  const [submitStatus, setSubmitStatus] = useState<FormStatus>('idle');

  // 表单配置
  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: '',
      email: '',
      purpose: '',
      region: '',
      message: '',
    },
  });

  // 表单提交配置
  const formSubmissionConfig = useMemo(
    () => ({
      maxRetries: 3,
      retryDelay: 1000,
      showToast: true,
      onSuccess: () => setSubmitStatus('success'),
      onError: (error: unknown) => {
        setSubmitStatus('error');
        // eslint-disable-next-line no-console
        console.error('表单提交失败:', error);
      },
    }),
    []
  );

  // 使用表单提交Hook
  const {
    isSubmitting,
    error: submissionError,
    canRetry,
    submitWithErrorHandling,
    retrySubmission,
    clearError,
  } = useFormSubmission(formSubmissionConfig);

  // 表单提交处理
  const onSubmit = useCallback(
    async (_data: ContactFormData): Promise<void> => {
      clearError();
      setSubmitStatus('idle');

      await submitWithErrorHandling(async () => {
        // 模拟API调用
        await new Promise((resolve, reject) => {
          setTimeout(() => {
            if (Math.random() > 0.7) {
              reject(new Error('模拟网络错误'));
            } else {
              resolve(undefined);
            }
          }, 2000);
        });

        // 成功后重置表单
        form.reset();
      });
    },
    [submitWithErrorHandling, clearError, form]
  );

  // 重试处理
  const handleRetry = useCallback(async (): Promise<void> => {
    const formData = form.getValues();
    await retrySubmission(async () => {
      await onSubmit(formData);
    });
  }, [form, retrySubmission, onSubmit]);

  return {
    form,
    submitStatus,
    isSubmitting,
    submissionError,
    canRetry,
    onSubmit,
    handleRetry,
  };
};

/**
 * 表单配置Hook
 *
 * 提供可配置的表单设置
 */
export const useContactFormConfig = (): {
  maxRetries: number;
  retryDelay: number;
  showToast: boolean;
} => {
  return useMemo(
    () => ({
      maxRetries: 3,
      retryDelay: 1000,
      showToast: true,
      // 可以根据需要添加更多配置选项
    }),
    []
  );
};
