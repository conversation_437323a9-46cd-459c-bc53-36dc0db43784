import { useCallback, useState } from 'react';

/**
 * 表单提交错误类型
 */
export class FormSubmissionError extends Error {
  constructor(
    message: string,
    public code: string,
    public retryable = false
  ) {
    super(message);
    this.name = 'FormSubmissionError';
  }
}

export class NetworkError extends FormSubmissionError {
  constructor(message = '网络连接失败，请检查网络后重试') {
    super(message, 'NETWORK_ERROR', true);
  }
}

export class ValidationError extends FormSubmissionError {
  constructor(message = '表单数据验证失败，请检查输入') {
    super(message, 'VALIDATION_ERROR', false);
  }
}

export class ServerError extends FormSubmissionError {
  constructor(message = '服务器错误，请稍后重试') {
    super(message, 'SERVER_ERROR', true);
  }
}

export class RateLimitError extends FormSubmissionError {
  constructor(message = '请求过于频繁，请稍后再试') {
    super(message, 'RATE_LIMIT_ERROR', true);
  }
}

/**
 * 表单提交状态
 */
interface FormSubmissionState {
  isSubmitting: boolean;
  error: string | null;
  retryCount: number;
  canRetry: boolean;
}

/**
 * 表单提交Hook配置
 */
interface UseFormSubmissionOptions {
  maxRetries?: number;
  retryDelay?: number;
  showToast?: boolean;
  onSuccess?: () => void;
  onError?: (error: FormSubmissionError) => void;
}

/**
 * 错误分类和处理函数
 */
const handleFormError = (error: unknown): FormSubmissionError => {
  if (error instanceof FormSubmissionError) {
    return error;
  }

  if (error instanceof TypeError && error.message.includes('fetch')) {
    return new NetworkError();
  }

  if (error instanceof Error) {
    if (error.message.includes('400')) {
      return new ValidationError();
    }
    if (error.message.includes('429')) {
      return new RateLimitError();
    }
    if (
      error.message.includes('500') ||
      error.message.includes('502') ||
      error.message.includes('503')
    ) {
      return new ServerError();
    }
  }

  return new FormSubmissionError('未知错误，请稍后重试', 'UNKNOWN_ERROR', true);
};

/**
 * 表单提交Hook
 *
 * 提供统一的表单提交错误处理、重试机制和用户反馈
 */
export function useFormSubmission(options: UseFormSubmissionOptions = {}): {
  isSubmitting: boolean;
  error: string | null;
  retryCount: number;
  canRetry: boolean;
  submitWithErrorHandling: (submitFn: () => Promise<void>) => Promise<void>;
  retrySubmission: (submitFn: () => Promise<void>) => Promise<void>;
  clearError: () => void;
} {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    showToast = true,
    onSuccess,
    onError,
  } = options;

  const [state, setState] = useState<FormSubmissionState>({
    isSubmitting: false,
    error: null,
    retryCount: 0,
    canRetry: false,
  });

  /**
   * 延迟函数
   */
  const delay = (ms: number): Promise<void> =>
    new Promise((resolve) => setTimeout(resolve, ms));

  /**
   * 错误分类和处理
   */
  // 使用外部的错误处理函数
  const handleError = useCallback((error: unknown): FormSubmissionError => {
    return handleFormError(error);
  }, []);

  /**
   * 重试逻辑
   */
  const retry = useCallback(
    async (submitFn: () => Promise<void>): Promise<void> => {
      if (state.retryCount >= maxRetries) {
        throw new FormSubmissionError(
          '重试次数已达上限',
          'MAX_RETRIES_EXCEEDED',
          false
        );
      }

      setState((prev) => ({ ...prev, retryCount: prev.retryCount + 1 }));

      await delay(retryDelay * Math.pow(2, state.retryCount)); // 指数退避

      return submitFn();
    },
    [state.retryCount, maxRetries, retryDelay]
  );

  /**
   * 主要提交函数
   */
  const submitWithErrorHandling = useCallback(
    async (submitFn: () => Promise<void>): Promise<void> => {
      try {
        setState((prev) => ({ ...prev, isSubmitting: true, error: null }));

        await submitFn();

        setState((prev) => ({
          ...prev,
          isSubmitting: false,
          retryCount: 0,
          canRetry: false,
        }));

        if (showToast) {
          // TODO: 集成toast通知系统
          // eslint-disable-next-line no-console
          console.log('提交成功！');
        }

        onSuccess?.();
      } catch (error) {
        const formError = handleError(error);

        setState((prev) => ({
          ...prev,
          isSubmitting: false,
          error: formError.message,
          canRetry: formError.retryable && prev.retryCount < maxRetries,
        }));

        if (showToast) {
          // TODO: 集成toast通知系统
          // eslint-disable-next-line no-console
          console.error('提交失败:', formError.message);
        }

        onError?.(formError);

        throw formError;
      }
    },
    [handleError, maxRetries, showToast, onSuccess, onError]
  );

  /**
   * 手动重试函数
   */
  const retrySubmission = useCallback(
    async (submitFn: () => Promise<void>): Promise<void> => {
      if (!state.canRetry) {
        return;
      }

      try {
        await retry(submitFn);
        await submitWithErrorHandling(submitFn);
      } catch {
        // 错误已在submitWithErrorHandling中处理
      }
    },
    [state.canRetry, retry, submitWithErrorHandling]
  );

  /**
   * 清除错误状态
   */
  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null, canRetry: false }));
  }, []);

  return {
    ...state,
    submitWithErrorHandling,
    retrySubmission,
    clearError,
  };
}
