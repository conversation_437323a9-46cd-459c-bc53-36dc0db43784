import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { UnderConstruction } from '@/components/content/under-construction';

/**
 * 关于页面
 *
 * 展示公司介绍、团队信息和企业文化的页面，目前使用建设中模板。
 * 支持多语言显示，配置了完整的SEO metadata。
 */

interface AboutPageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * 生成页面metadata
 */
export async function generateMetadata({
  params,
}: AboutPageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.about' });

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
    },
  };
}

/**
 * 关于页面组件
 */
export default function AboutPage({
  params: _params,
}: AboutPageProps): React.JSX.Element {
  return <UnderConstruction />;
}
