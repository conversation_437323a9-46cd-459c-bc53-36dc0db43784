/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */
'use client';

import React from 'react';
import {
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw,
  XCircle,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslationQuality } from '@/hooks/use-translation-quality';
import type {
  IssueSeverity,
  TranslationIssue,
} from '@/lib/i18n/translation-quality-manager';

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

/**
 * 翻译质量管理仪表板组件
 *
 * 功能特性：
 * - 翻译质量概览和统计
 * - 问题列表和详细信息
 * - 覆盖率报告和质量评分
 * - 实时监控和自动检查
 * - 开发工具集成
 */

// 严重程度颜色映射
const SEVERITY_COLORS: Record<
  IssueSeverity,
  'default' | 'secondary' | 'destructive' | 'outline'
> = {
  critical: 'destructive',
  high: 'destructive',
  medium: 'default',
  low: 'secondary',
};

// 严重程度图标映射
const SEVERITY_ICONS: Record<IssueSeverity, React.ReactNode> = {
  critical: <XCircle className='h-4 w-4' />,
  high: <AlertTriangle className='h-4 w-4' />,
  medium: <AlertTriangle className='h-4 w-4' />,
  low: <CheckCircle className='h-4 w-4' />,
};

interface TranslationQualityDashboardProps {
  className?: string;
  showInProduction?: boolean;
}

// 统计卡片组件
interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
}

function StatsCard({
  title,
  value,
  description,
  icon,
}: StatsCardProps): React.ReactElement {
  return (
    <Card>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium'>{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>{value}</div>
        {description !== undefined && description.length > 0 && (
          <p className='text-muted-foreground text-xs'>{description}</p>
        )}
      </CardContent>
    </Card>
  );
}

// 问题列表组件
interface IssueListProps {
  issues: TranslationIssue[];
}

function IssueList({ issues }: IssueListProps): React.ReactElement {
  if (issues.length === 0) {
    return (
      <div className='text-muted-foreground py-8 text-center'>
        <CheckCircle className='mx-auto mb-4 h-12 w-12' />
        <p>没有发现翻译问题</p>
      </div>
    );
  }

  return (
    <div className='space-y-2'>
      {issues.map((issue, index) => (
        <div
          key={index}
          className='flex items-center justify-between rounded-lg border p-3'
        >
          <div className='flex items-center space-x-3'>
            {SEVERITY_ICONS[issue.severity]}
            <div>
              <p className='font-medium'>{issue.keyPath}</p>
              <p className='text-muted-foreground text-sm'>{issue.message}</p>
            </div>
          </div>
          <Badge variant={SEVERITY_COLORS[issue.severity]}>
            {issue.severity}
          </Badge>
        </div>
      ))}
    </div>
  );
}

/**
 * 翻译质量管理仪表板组件
 */
export function TranslationQualityDashboard({
  className,
  showInProduction = false,
}: TranslationQualityDashboardProps): React.ReactElement | null {
  // 在生产环境中隐藏（除非明确启用）
  if (process.env.NODE_ENV === 'production' && !showInProduction) {
    return null;
  }

  return (
    <DashboardContent
      {...(className !== undefined && className.length > 0 && { className })}
    />
  );
}

// 主要仪表板内容组件
function DashboardContent({
  className,
}: {
  className?: string;
}): React.ReactElement {
  const { issues, exportReport, isChecking, checkAllModules, getQualityScore } =
    useTranslationQuality({
      autoCheck: true,
      devModeOnly: true,
      debug: true,
    });

  // 计算数据
  const filteredIssues = issues;
  const qualityScore = getQualityScore();
  const currentQuality = qualityScore?.overall ?? 0;

  // 处理导出
  const handleExport = (format: 'json' | 'csv'): void => {
    try {
      const data = exportReport(format);
      const blob = new Blob([data], {
        type: format === 'json' ? 'application/json' : 'text/csv',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `translation-quality-report.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error(
          'Export failed:',
          error instanceof Error ? error.message : 'Unknown error'
        );
      }
    }
  };

  return (
    <div className={className ?? ''}>
      {/* 简化的仪表板内容 */}
      <Card>
        <CardHeader>
          <CardTitle>翻译质量仪表板</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='mb-6 grid grid-cols-1 gap-4 md:grid-cols-3'>
            <StatsCard
              title='总问题数'
              value={filteredIssues.length}
              icon={<AlertTriangle className='h-4 w-4' />}
            />
            <StatsCard
              title='质量评分'
              value={`${Math.round(currentQuality)}%`}
              icon={<CheckCircle className='h-4 w-4' />}
            />
            <StatsCard
              title='检查状态'
              value={isChecking ? '检查中...' : '已完成'}
              icon={<RefreshCw className='h-4 w-4' />}
            />
          </div>

          <div className='mb-4 flex gap-2'>
            <Button
              onClick={() => {
                void checkAllModules();
              }}
              disabled={isChecking}
            >
              {isChecking ? (
                <RefreshCw className='h-4 w-4 animate-spin' />
              ) : (
                <RefreshCw className='h-4 w-4' />
              )}
              检查翻译
            </Button>
            <Button variant='outline' onClick={() => handleExport('json')}>
              <Download className='h-4 w-4' />
              导出报告
            </Button>
          </div>

          <IssueList issues={filteredIssues} />
        </CardContent>
      </Card>
    </div>
  );
}
