#!/usr/bin/env node

/**
 * ESLint 批量修复脚本
 * 
 * 自动修复常见的 ESLint 错误类型：
 * 1. Console 语句包装
 * 2. 严格布尔表达式修复
 * 3. Nullish coalescing 替换
 * 4. 缺失返回类型添加
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ESLintBatchFixer {
  constructor() {
    this.projectRoot = process.cwd();
    this.fixedFiles = new Set();
    this.stats = {
      consoleStatements: 0,
      strictBooleans: 0,
      nullishCoalescing: 0,
      returnTypes: 0,
      totalFiles: 0
    };
  }

  /**
   * 主要修复流程
   */
  async run() {
    console.log('🚀 开始批量修复 ESLint 错误...\n');
    
    try {
      // 1. 修复 console 语句
      await this.fixConsoleStatements();
      
      // 2. 修复严格布尔表达式
      await this.fixStrictBooleanExpressions();
      
      // 3. 修复 nullish coalescing
      await this.fixNullishCoalescing();
      
      // 4. 添加缺失的返回类型
      await this.addMissingReturnTypes();
      
      // 5. 显示统计结果
      this.showStats();
      
      // 6. 运行最终检查
      await this.runFinalCheck();
      
    } catch (error) {
      console.error('❌ 修复过程中出现错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 修复 console 语句
   */
  async fixConsoleStatements() {
    console.log('📝 修复 console 语句...');
    
    const files = this.getTypeScriptFiles();
    
    for (const file of files) {
      let content = fs.readFileSync(file, 'utf-8');
      let modified = false;
      
      // 包装 console.log 语句
      const consoleRegex = /^(\s*)(console\.(log|warn|error|info|debug)\([^)]*\);?)$/gm;
      content = content.replace(consoleRegex, (match, indent, statement) => {
        if (!match.includes('process.env.NODE_ENV')) {
          modified = true;
          this.stats.consoleStatements++;
          return `${indent}if (process.env.NODE_ENV === 'development') {\n${indent}  ${statement}\n${indent}}`;
        }
        return match;
      });
      
      if (modified) {
        fs.writeFileSync(file, content);
        this.fixedFiles.add(file);
      }
    }
    
    console.log(`✅ 修复了 ${this.stats.consoleStatements} 个 console 语句\n`);
  }

  /**
   * 修复严格布尔表达式
   */
  async fixStrictBooleanExpressions() {
    console.log('🔍 修复严格布尔表达式...');
    
    const files = this.getTypeScriptFiles();
    
    for (const file of files) {
      let content = fs.readFileSync(file, 'utf-8');
      let modified = false;
      
      // 修复 nullable 对象检查
      const nullableObjectRegex = /(\w+)\s*&&\s*(\w+\.\w+)/g;
      content = content.replace(nullableObjectRegex, (match, obj, prop) => {
        if (obj === prop.split('.')[0]) {
          modified = true;
          this.stats.strictBooleans++;
          return `${obj} !== null && ${obj} !== undefined && ${prop}`;
        }
        return match;
      });
      
      // 修复 nullable 字符串检查
      const nullableStringRegex = /(\w+)\s*&&\s*(\w+)\.length/g;
      content = content.replace(nullableStringRegex, (match, str1, str2) => {
        if (str1 === str2) {
          modified = true;
          this.stats.strictBooleans++;
          return `${str1} !== null && ${str1} !== undefined && ${str1}.length > 0`;
        }
        return match;
      });
      
      if (modified) {
        fs.writeFileSync(file, content);
        this.fixedFiles.add(file);
      }
    }
    
    console.log(`✅ 修复了 ${this.stats.strictBooleans} 个严格布尔表达式\n`);
  }

  /**
   * 修复 nullish coalescing
   */
  async fixNullishCoalescing() {
    console.log('🔄 修复 nullish coalescing...');
    
    const files = this.getTypeScriptFiles();
    
    for (const file of files) {
      let content = fs.readFileSync(file, 'utf-8');
      let modified = false;
      
      // 替换 || 为 ??（仅在安全的情况下）
      const logicalOrRegex = /(\w+(?:\.\w+)*)\s*\|\|\s*([^|&\n]+)/g;
      content = content.replace(logicalOrRegex, (match, left, right) => {
        // 只在明显是 null/undefined 检查的情况下替换
        if (right.trim().match(/^['"`].*['"`]$|^\d+$|^true$|^false$|^\{\}$|^\[\]$/)) {
          modified = true;
          this.stats.nullishCoalescing++;
          return `${left} ?? ${right}`;
        }
        return match;
      });
      
      if (modified) {
        fs.writeFileSync(file, content);
        this.fixedFiles.add(file);
      }
    }
    
    console.log(`✅ 修复了 ${this.stats.nullishCoalescing} 个 nullish coalescing\n`);
  }

  /**
   * 添加缺失的返回类型
   */
  async addMissingReturnTypes() {
    console.log('📋 添加缺失的返回类型...');
    
    const files = this.getTypeScriptFiles();
    
    for (const file of files) {
      let content = fs.readFileSync(file, 'utf-8');
      let modified = false;
      
      // 为箭头函数添加返回类型
      const arrowFunctionRegex = /(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{/g;
      content = content.replace(arrowFunctionRegex, (match, name) => {
        if (!match.includes(': ')) {
          modified = true;
          this.stats.returnTypes++;
          return match.replace('=>', ': void =>');
        }
        return match;
      });
      
      // 为普通函数添加返回类型
      const functionRegex = /function\s+(\w+)\s*\([^)]*\)\s*\{/g;
      content = content.replace(functionRegex, (match, name) => {
        if (!match.includes(': ')) {
          modified = true;
          this.stats.returnTypes++;
          return match.replace('{', ': void {');
        }
        return match;
      });
      
      if (modified) {
        fs.writeFileSync(file, content);
        this.fixedFiles.add(file);
      }
    }
    
    console.log(`✅ 添加了 ${this.stats.returnTypes} 个返回类型\n`);
  }

  /**
   * 获取所有 TypeScript 文件
   */
  getTypeScriptFiles() {
    const files = [];
    
    const scanDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          scanDir(fullPath);
        } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
          files.push(fullPath);
        }
      }
    };
    
    scanDir(path.join(this.projectRoot, 'src'));
    return files;
  }

  /**
   * 显示统计结果
   */
  showStats() {
    console.log('📊 修复统计结果:');
    console.log(`   Console 语句: ${this.stats.consoleStatements}`);
    console.log(`   严格布尔表达式: ${this.stats.strictBooleans}`);
    console.log(`   Nullish coalescing: ${this.stats.nullishCoalescing}`);
    console.log(`   返回类型: ${this.stats.returnTypes}`);
    console.log(`   修改的文件: ${this.fixedFiles.size}`);
    console.log('');
  }

  /**
   * 运行最终检查
   */
  async runFinalCheck() {
    console.log('🔍 运行最终 ESLint 检查...');
    
    try {
      const result = execSync('pnpm lint 2>&1 | grep -c "Error:" || echo "0"', { 
        encoding: 'utf-8',
        cwd: this.projectRoot 
      });
      
      const errorCount = parseInt(result.trim());
      console.log(`📈 剩余错误数量: ${errorCount}`);
      
      if (errorCount === 0) {
        console.log('🎉 恭喜！所有 ESLint 错误已修复！');
      } else {
        console.log(`📝 还需要手动修复 ${errorCount} 个错误`);
      }
      
    } catch (error) {
      console.log('⚠️  无法运行 ESLint 检查，请手动验证');
    }
  }
}

// 运行修复脚本
if (require.main === module) {
  const fixer = new ESLintBatchFixer();
  fixer.run().catch(console.error);
}

module.exports = ESLintBatchFixer;
