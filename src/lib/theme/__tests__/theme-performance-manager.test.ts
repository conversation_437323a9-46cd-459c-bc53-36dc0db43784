/**
 * 主题性能管理器测试
 * 
 * 测试覆盖：
 * - 管理器初始化
 * - 配置管理
 * - 主题加载
 * - 缓存管理
 * - 性能监控
 * - 事件处理
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ThemePerformanceManager } from '../theme-performance-manager';

// Mock DOM APIs
const mockFetch = vi.fn();
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};

const mockPerformance = {
  now: vi.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
  },
};

const mockWorker = {
  postMessage: vi.fn(),
  terminate: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
};

const mockURL = {
  createObjectURL: vi.fn(() => 'blob:mock-url'),
  revokeObjectURL: vi.fn(),
};

const mockBlob = vi.fn(() => ({}));

// Mock global APIs
Object.defineProperty(global, 'fetch', {
  value: mockFetch,
  writable: true,
});

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true,
});

Object.defineProperty(global, 'Worker', {
  value: vi.fn(() => mockWorker),
  writable: true,
});

Object.defineProperty(global, 'URL', {
  value: mockURL,
  writable: true,
});

Object.defineProperty(global, 'Blob', {
  value: mockBlob,
  writable: true,
});

Object.defineProperty(global, 'window', {
  value: {
    PerformanceObserver: vi.fn(),
  },
  writable: true,
});

describe('ThemePerformanceManager', () => {
  let manager: ThemePerformanceManager;

  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks();
    
    // 设置 fetch mock
    mockFetch.mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({
        name: 'Test Theme',
        colors: { primary: '#000000' },
      }),
    });
  });

  afterEach(() => {
    if (manager) {
      manager.destroy();
    }
  });

  describe('初始化', () => {
    it('应该能够创建管理器实例', () => {
      manager = new ThemePerformanceManager();
      expect(manager).toBeInstanceOf(ThemePerformanceManager);
    });

    it('应该能够使用初始配置创建实例', () => {
      const initialConfig = {
        enablePreloading: false,
        cacheSize: 100,
      };
      
      manager = new ThemePerformanceManager(initialConfig);
      const config = manager.getConfig();
      
      expect(config.enablePreloading).toBe(false);
      expect(config.cacheSize).toBe(100);
    });

    it('应该设置默认配置', () => {
      manager = new ThemePerformanceManager();
      const config = manager.getConfig();
      
      expect(config.enablePreloading).toBe(true);
      expect(config.enableCaching).toBe(true);
      expect(config.enableLazyLoading).toBe(true);
      expect(config.cacheSize).toBe(50);
      expect(config.preloadThemes).toEqual(['light', 'dark']);
    });

    it('应该初始化性能指标', () => {
      manager = new ThemePerformanceManager();
      const metrics = manager.getMetrics();
      
      expect(metrics.loadTime).toBe(0);
      expect(metrics.cacheHitRate).toBe(0);
      expect(metrics.memoryUsage).toBe(0);
      expect(metrics.networkRequests).toBe(0);
      expect(metrics.totalThemesLoaded).toBe(0);
    });
  });

  describe('配置管理', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager();
    });

    it('应该能够获取当前配置', () => {
      const config = manager.getConfig();
      
      expect(config).toHaveProperty('enablePreloading');
      expect(config).toHaveProperty('enableCaching');
      expect(config).toHaveProperty('cacheSize');
      expect(config).toHaveProperty('preloadThemes');
    });

    it('应该能够更新配置', () => {
      const updates = {
        enablePreloading: false,
        cacheSize: 100,
      };
      
      manager.updateConfig(updates);
      const config = manager.getConfig();
      
      expect(config.enablePreloading).toBe(false);
      expect(config.cacheSize).toBe(100);
    });

    it('应该在配置更新时发出事件', (done) => {
      manager.addEventListener('performance-change', (event: any) => {
        const { type } = event.detail;
        if (type === 'performance-update') {
          done();
        }
      });
      
      manager.updateConfig({ enablePreloading: false });
    });
  });

  describe('主题加载', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager();
    });

    it('应该能够加载主题', async () => {
      const themeData = await manager.loadTheme('test-theme');
      
      expect(mockFetch).toHaveBeenCalledWith('/api/themes/test-theme');
      expect(themeData).toEqual({
        name: 'Test Theme',
        colors: { primary: '#000000' },
      });
    });

    it('应该更新加载状态', async () => {
      const loadPromise = manager.loadTheme('test-theme');
      
      // 检查加载中状态
      const loadingState = manager.getLoadingState();
      expect(loadingState.isLoading).toBe(true);
      expect(loadingState.currentTheme).toBe('test-theme');
      
      await loadPromise;
      
      // 检查加载完成状态
      const completedState = manager.getLoadingState();
      expect(completedState.isLoading).toBe(false);
      expect(completedState.loadedThemes.has('test-theme')).toBe(true);
    });

    it('应该发出加载事件', (done) => {
      let eventCount = 0;
      
      manager.addEventListener('performance-change', (event: any) => {
        const { type } = event.detail;
        eventCount++;
        
        if (type === 'theme-load-start' && eventCount === 1) {
          expect(event.detail.theme).toBe('test-theme');
        } else if (type === 'theme-load-complete' && eventCount === 2) {
          expect(event.detail.theme).toBe('test-theme');
          done();
        }
      });
      
      manager.loadTheme('test-theme');
    });

    it('应该处理加载错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      
      await expect(manager.loadTheme('error-theme')).rejects.toThrow('Network error');
      
      const loadingState = manager.getLoadingState();
      expect(loadingState.failedThemes.has('error-theme')).toBe(true);
    });

    it('应该更新性能指标', async () => {
      await manager.loadTheme('test-theme');
      
      const metrics = manager.getMetrics();
      expect(metrics.networkRequests).toBe(1);
      expect(metrics.totalThemesLoaded).toBe(1);
      expect(metrics.loadTime).toBeGreaterThan(0);
    });
  });

  describe('缓存管理', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager();
    });

    it('应该缓存加载的主题', async () => {
      // 第一次加载
      await manager.loadTheme('cached-theme');
      expect(mockFetch).toHaveBeenCalledTimes(1);
      
      // 第二次加载应该使用缓存
      await manager.loadTheme('cached-theme');
      expect(mockFetch).toHaveBeenCalledTimes(1); // 仍然是1次
    });

    it('应该能够清除缓存', async () => {
      await manager.loadTheme('cached-theme');
      
      manager.clearCache();
      
      // 清除缓存后再次加载应该重新请求
      await manager.loadTheme('cached-theme');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('应该能够预热缓存', async () => {
      const themes = ['theme1', 'theme2', 'theme3'];
      
      await manager.warmupCache(themes);
      
      expect(mockFetch).toHaveBeenCalledTimes(3);
      expect(mockFetch).toHaveBeenCalledWith('/api/themes/theme1');
      expect(mockFetch).toHaveBeenCalledWith('/api/themes/theme2');
      expect(mockFetch).toHaveBeenCalledWith('/api/themes/theme3');
    });

    it('应该提供缓存信息', async () => {
      await manager.loadTheme('test-theme');
      
      const cacheInfo = manager.getCacheInfo();
      expect(cacheInfo.items).toBe(1);
      expect(cacheInfo.size).toBeGreaterThan(0);
    });

    it('应该计算缓存命中率', async () => {
      // 加载主题（缓存未命中）
      await manager.loadTheme('theme1');
      
      // 再次加载相同主题（缓存命中）
      await manager.loadTheme('theme1');
      
      const metrics = manager.getMetrics();
      expect(metrics.cacheHitRate).toBeGreaterThan(0);
    });
  });

  describe('性能优化', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager();
    });

    it('应该能够优化性能', () => {
      expect(() => {
        manager.optimizePerformance();
      }).not.toThrow();
    });

    it('应该能够重置性能指标', async () => {
      // 先加载一些主题产生指标
      await manager.loadTheme('test-theme');
      
      let metrics = manager.getMetrics();
      expect(metrics.totalThemesLoaded).toBe(1);
      
      // 重置指标
      manager.resetMetrics();
      
      metrics = manager.getMetrics();
      expect(metrics.totalThemesLoaded).toBe(0);
      expect(metrics.loadTime).toBe(0);
    });

    it('应该生成性能报告', () => {
      const report = manager.getPerformanceReport();
      
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('cacheInfo');
      expect(report).toHaveProperty('loadingState');
      expect(report).toHaveProperty('recommendations');
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    it('应该提供性能建议', () => {
      // 设置低缓存命中率
      manager['metrics'].cacheHitRate = 30;
      
      const report = manager.getPerformanceReport();
      expect(report.recommendations.length).toBeGreaterThan(0);
      expect(report.recommendations.some(r => r.includes('缓存'))).toBe(true);
    });
  });

  describe('事件处理', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager();
    });

    it('应该发出性能更新事件', (done) => {
      manager.addEventListener('performance-change', (event: any) => {
        const { type, metrics } = event.detail;
        expect(type).toBe('performance-update');
        expect(metrics).toBeDefined();
        done();
      });
      
      manager.updateConfig({ enablePreloading: false });
    });

    it('应该发出内存警告事件', (done) => {
      manager.addEventListener('performance-change', (event: any) => {
        const { type } = event.detail;
        if (type === 'memory-warning') {
          done();
        }
      });
      
      // 模拟高内存使用
      manager['metrics'].memoryUsage = 150; // 超过默认阈值100MB
      manager['emitEvent']('memory-warning', undefined, { memoryUsage: 150 });
    });
  });

  describe('销毁', () => {
    it('应该能够销毁管理器', () => {
      manager = new ThemePerformanceManager();
      
      expect(() => {
        manager.destroy();
      }).not.toThrow();
      
      expect(manager.isReady()).toBe(false);
    });

    it('应该清理资源', () => {
      manager = new ThemePerformanceManager();
      
      manager.destroy();
      
      expect(mockWorker.terminate).toHaveBeenCalled();
    });
  });

  describe('压缩功能', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager({ compressionEnabled: true });
    });

    it('应该创建压缩 Worker', () => {
      expect(global.Worker).toHaveBeenCalled();
      expect(mockBlob).toHaveBeenCalled();
    });

    it('应该处理压缩错误', () => {
      // 模拟 Worker 创建失败
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      (global.Worker as any).mockImplementationOnce(() => {
        throw new Error('Worker creation failed');
      });
      
      manager = new ThemePerformanceManager({ compressionEnabled: true });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to create compression worker:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('内存监控', () => {
    beforeEach(() => {
      manager = new ThemePerformanceManager({ performanceMonitoring: true });
    });

    it('应该监控内存使用', () => {
      const metrics = manager.getMetrics();
      expect(metrics.memoryUsage).toBeGreaterThanOrEqual(0);
    });

    it('应该更新峰值内存使用', () => {
      // 模拟内存使用增加
      manager['metrics'].memoryUsage = 80;
      manager['metrics'].peakMemoryUsage = 60;
      
      // 触发内存检查
      manager['setupMemoryMonitoring']();
      
      expect(manager['metrics'].peakMemoryUsage).toBeGreaterThanOrEqual(80);
    });
  });
});
