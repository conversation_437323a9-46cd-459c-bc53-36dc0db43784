/**
 * 智能主题适配 React Hook
 *
 * 功能特性：
 * - 自动主题适配建议
 * - 用户行为学习和记录
 * - 环境感知和响应
 * - 适配历史管理
 * - 实时主题切换
 * - 性能优化和缓存
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTheme } from 'next-themes';
import {
  type EnvironmentData,
  type SupportedTheme,
  type ThemeAdaptationSuggestion,
  type UserActivity,
  type UserBehaviorData,
  smartThemeAdapter,
} from '@/lib/theme/smart-theme-adapter';

// Hook 配置选项
export interface UseSmartThemeAdaptationOptions {
  enableAutoAdaptation?: boolean; // 启用自动适配
  adaptationInterval?: number; // 适配检查间隔（毫秒）
  enableLearning?: boolean; // 启用学习功能
  enableNotifications?: boolean; // 启用适配通知
  confidenceThreshold?: number; // 适配建议置信度阈值
  debugMode?: boolean; // 调试模式
}

// Hook 返回状态
export interface SmartThemeAdaptationState {
  // 当前状态
  currentSuggestion: ThemeAdaptationSuggestion | null;
  isAdapting: boolean;
  isLearning: boolean;

  // 用户数据
  userBehavior: UserBehaviorData | null;
  adaptationHistory: ThemeAdaptationSuggestion[];

  // 环境数据
  environmentData: Partial<EnvironmentData> | null;

  // 统计信息
  statistics: {
    totalAdaptations: number;
    acceptedAdaptations: number;
    rejectedAdaptations: number;
    acceptanceRate: number;
  };

  // 错误状态
  error: string | null;
}

// Hook 返回操作
export interface SmartThemeAdaptationActions {
  // 主题操作
  requestAdaptation: () => Promise<ThemeAdaptationSuggestion | null>;
  acceptSuggestion: (suggestion: ThemeAdaptationSuggestion) => void;
  rejectSuggestion: (suggestion: ThemeAdaptationSuggestion) => void;
  applyTheme: (theme: SupportedTheme) => void;

  // 学习操作
  recordActivity: (activity: UserActivity, duration?: number) => void;
  updateEnvironment: (data: Partial<EnvironmentData>) => void;

  // 配置操作
  toggleAutoAdaptation: () => void;
  toggleLearning: () => void;
  setConfidenceThreshold: (threshold: number) => void;

  // 数据操作
  exportUserData: () => UserBehaviorData;
  importUserData: (data: UserBehaviorData) => void;
  clearAllData: () => void;

  // 调试操作
  getDebugInfo: () => Record<string, unknown>;
}

/**
 * 智能主题适配 Hook
 */
export function useSmartThemeAdaptation(
  options: UseSmartThemeAdaptationOptions = {}
): [SmartThemeAdaptationState, SmartThemeAdaptationActions] {
  const {
    enableAutoAdaptation = true,
    adaptationInterval = 60000, // 1分钟
    enableLearning = true,
    enableNotifications = false,
    confidenceThreshold = 0.7,
    debugMode = false,
  } = options;

  const { theme, setTheme } = useTheme();

  // 状态管理
  const [state, setState] = useState<SmartThemeAdaptationState>({
    currentSuggestion: null,
    isAdapting: false,
    isLearning: enableLearning,
    userBehavior: null,
    adaptationHistory: [],
    environmentData: null,
    statistics: {
      totalAdaptations: 0,
      acceptedAdaptations: 0,
      rejectedAdaptations: 0,
      acceptanceRate: 0,
    },
    error: null,
  });

  // 引用管理
  const adaptationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const sessionStartRef = useRef<number>(Date.now());
  const currentActivityRef = useRef<UserActivity>('browsing');
  const lastThemeRef = useRef<string | undefined>(theme);

  /**
   * 更新状态的辅助函数
   */
  const updateState = useCallback(
    (updates: Partial<SmartThemeAdaptationState>) => {
      setState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  /**
   * 记录错误
   */
  const recordError = useCallback(
    (error: string) => {
      updateState({ error });
      if (debugMode && process.env.NODE_ENV === 'development') {
        console.error('[SmartThemeAdaptation]', error);
      }
    },
    [debugMode, updateState]
  );

  /**
   * 获取当前时间段
   */
  const getTimeOfDay = useCallback((date: Date) => {
    const hour = date.getHours();
    if (hour >= 5 && hour < 7) return 'dawn';
    if (hour >= 7 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 20) return 'evening';
    return 'night';
  }, []);

  /**
   * 获取当前环境数据
   */
  const getCurrentEnvironmentData =
    useCallback((): Partial<EnvironmentData> => {
      const now = new Date();

      return {
        time: {
          current: now,
          timeOfDay: getTimeOfDay(now),
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
        device: {
          powerSaving: false,
        },
        accessibility: {
          highContrast:
            typeof window !== 'undefined'
              ? window.matchMedia('(prefers-contrast: high)').matches
              : false,
          reducedMotion:
            typeof window !== 'undefined'
              ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
              : false,
          largeText: false,
        },
      };
    }, []);

  /**
   * 请求主题适配建议
   */
  const requestAdaptation =
    useCallback(async (): Promise<ThemeAdaptationSuggestion | null> => {
      try {
        updateState({ isAdapting: true, error: null });

        const environmentData = getCurrentEnvironmentData();
        const suggestion = await smartThemeAdapter.generateAdaptationSuggestion(
          (theme as SupportedTheme) || 'auto',
          environmentData
        );

        updateState({
          currentSuggestion: suggestion,
          environmentData,
          isAdapting: false,
        });

        // 更新统计信息
        setState((prev) => ({
          ...prev,
          statistics: {
            ...prev.statistics,
            totalAdaptations: prev.statistics.totalAdaptations + 1,
          },
        }));

        if (debugMode && process.env.NODE_ENV === 'development') {
          console.log(
            '[SmartThemeAdaptation] Generated suggestion:',
            suggestion
          );
        }

        return suggestion;
      } catch (error) {
        recordError(`Failed to generate adaptation suggestion: ${error}`);
        updateState({ isAdapting: false });
        return null;
      }
    }, [theme, getCurrentEnvironmentData, updateState, recordError, debugMode]);

  /**
   * 接受适配建议
   */
  const acceptSuggestion = useCallback(
    (suggestion: ThemeAdaptationSuggestion) => {
      try {
        setTheme(suggestion.suggestedTheme);
        smartThemeAdapter.recordAdaptationFeedback(
          suggestion.timestamp.toString(),
          true
        );

        // 更新统计信息
        setState((prev) => ({
          ...prev,
          statistics: {
            ...prev.statistics,
            acceptedAdaptations: prev.statistics.acceptedAdaptations + 1,
            acceptanceRate:
              (prev.statistics.acceptedAdaptations + 1) /
              prev.statistics.totalAdaptations,
          },
          currentSuggestion: null,
        }));

        if (
          enableNotifications &&
          'Notification' in window &&
          Notification.permission === 'granted'
        ) {
          new Notification('主题已切换', {
            body: `已切换到${suggestion.suggestedTheme}主题：${suggestion.reasoning}`,
            icon: '/favicon.ico',
          });
        }

        if (debugMode && process.env.NODE_ENV === 'development') {
          console.log(
            '[SmartThemeAdaptation] Accepted suggestion:',
            suggestion
          );
        }
      } catch (error) {
        recordError(`Failed to accept suggestion: ${error}`);
      }
    },
    [setTheme, enableNotifications, debugMode, recordError]
  );

  /**
   * 拒绝适配建议
   */
  const rejectSuggestion = useCallback(
    (suggestion: ThemeAdaptationSuggestion) => {
      try {
        smartThemeAdapter.recordAdaptationFeedback(
          suggestion.timestamp.toString(),
          false
        );

        // 更新统计信息
        setState((prev) => ({
          ...prev,
          statistics: {
            ...prev.statistics,
            rejectedAdaptations: prev.statistics.rejectedAdaptations + 1,
            acceptanceRate:
              prev.statistics.acceptedAdaptations /
              prev.statistics.totalAdaptations,
          },
          currentSuggestion: null,
        }));

        if (debugMode && process.env.NODE_ENV === 'development') {
          console.log(
            '[SmartThemeAdaptation] Rejected suggestion:',
            suggestion
          );
        }
      } catch (error) {
        recordError(`Failed to reject suggestion: ${error}`);
      }
    },
    [debugMode, recordError]
  );

  /**
   * 应用主题
   */
  const applyTheme = useCallback(
    (newTheme: SupportedTheme) => {
      try {
        setTheme(newTheme);

        if (debugMode && process.env.NODE_ENV === 'development') {
          console.log('[SmartThemeAdaptation] Applied theme:', newTheme);
        }
      } catch (error) {
        recordError(`Failed to apply theme: ${error}`);
      }
    },
    [setTheme, debugMode, recordError]
  );

  /**
   * 记录用户活动
   */
  const recordActivity = useCallback(
    (activity: UserActivity, duration?: number) => {
      try {
        currentActivityRef.current = activity;

        if (enableLearning && theme) {
          const sessionDuration =
            duration || Date.now() - sessionStartRef.current;
          smartThemeAdapter.recordThemeUsage(
            theme as SupportedTheme,
            sessionDuration,
            activity
          );

          if (debugMode && process.env.NODE_ENV === 'development') {
            console.log('[SmartThemeAdaptation] Recorded activity:', {
              activity,
              duration: sessionDuration,
            });
          }
        }
      } catch (error) {
        recordError(`Failed to record activity: ${error}`);
      }
    },
    [enableLearning, theme, debugMode, recordError]
  );

  /**
   * 更新环境数据
   */
  const updateEnvironment = useCallback(
    (data: Partial<EnvironmentData>) => {
      updateState({
        environmentData: {
          ...state.environmentData,
          ...data,
        },
      });
    },
    [state.environmentData, updateState]
  );

  /**
   * 切换自动适配
   */
  const toggleAutoAdaptation = useCallback(() => {
    const newEnabled = !enableAutoAdaptation;

    if (newEnabled && !adaptationIntervalRef.current) {
      // 启动自动适配
      adaptationIntervalRef.current = setInterval(() => {
        requestAdaptation().then((suggestion) => {
          if (suggestion && suggestion.confidence >= confidenceThreshold) {
            // 自动接受高置信度的建议
            acceptSuggestion(suggestion);
          }
        });
      }, adaptationInterval);
    } else if (!newEnabled && adaptationIntervalRef.current) {
      // 停止自动适配
      clearInterval(adaptationIntervalRef.current);
      adaptationIntervalRef.current = null;
    }
  }, [
    enableAutoAdaptation,
    adaptationInterval,
    confidenceThreshold,
    requestAdaptation,
    acceptSuggestion,
  ]);

  /**
   * 切换学习功能
   */
  const toggleLearning = useCallback(() => {
    updateState({ isLearning: !state.isLearning });
  }, [state.isLearning, updateState]);

  /**
   * 设置置信度阈值
   */
  const setConfidenceThreshold = useCallback(
    (threshold: number) => {
      // 这里可以保存到配置中
      if (debugMode && process.env.NODE_ENV === 'development') {
        console.log(
          '[SmartThemeAdaptation] Set confidence threshold:',
          threshold
        );
      }
    },
    [debugMode]
  );

  /**
   * 导出用户数据
   */
  const exportUserData = useCallback((): UserBehaviorData => {
    return smartThemeAdapter.getUserBehaviorData();
  }, []);

  /**
   * 导入用户数据
   */
  const importUserData = useCallback(
    (data: UserBehaviorData) => {
      try {
        // 这里需要实现数据导入逻辑
        if (debugMode && process.env.NODE_ENV === 'development') {
          console.log('[SmartThemeAdaptation] Imported user data:', data);
        }
      } catch (error) {
        recordError(`Failed to import user data: ${error}`);
      }
    },
    [debugMode, recordError]
  );

  /**
   * 清除所有数据
   */
  const clearAllData = useCallback(() => {
    try {
      smartThemeAdapter.clearAllData();
      updateState({
        userBehavior: null,
        adaptationHistory: [],
        statistics: {
          totalAdaptations: 0,
          acceptedAdaptations: 0,
          rejectedAdaptations: 0,
          acceptanceRate: 0,
        },
      });

      if (debugMode && process.env.NODE_ENV === 'development') {
        console.log('[SmartThemeAdaptation] Cleared all data');
      }
    } catch (error) {
      recordError(`Failed to clear data: ${error}`);
    }
  }, [updateState, debugMode, recordError]);

  /**
   * 获取调试信息
   */
  const getDebugInfo = useCallback((): Record<string, unknown> => {
    return {
      state,
      options: {
        enableAutoAdaptation,
        adaptationInterval,
        enableLearning,
        enableNotifications,
        confidenceThreshold,
        debugMode,
      },
      currentTheme: theme,
      currentActivity: currentActivityRef.current,
      sessionDuration: Date.now() - sessionStartRef.current,
      userBehavior: smartThemeAdapter.getUserBehaviorData(),
      adaptationHistory: smartThemeAdapter.getAdaptationHistory(),
    };
  }, [
    state,
    enableAutoAdaptation,
    adaptationInterval,
    enableLearning,
    enableNotifications,
    confidenceThreshold,
    debugMode,
    theme,
  ]);

  // 初始化效果
  useEffect(() => {
    // 加载用户行为数据
    const userBehavior = smartThemeAdapter.getUserBehaviorData();
    const adaptationHistory = smartThemeAdapter.getAdaptationHistory();

    updateState({
      userBehavior,
      adaptationHistory,
    });

    // 请求通知权限
    if (
      enableNotifications &&
      'Notification' in window &&
      Notification.permission === 'default'
    ) {
      Notification.requestPermission();
    }

    // 启动自动适配
    if (enableAutoAdaptation) {
      adaptationIntervalRef.current = setInterval(() => {
        requestAdaptation().then((suggestion) => {
          if (suggestion && suggestion.confidence >= confidenceThreshold) {
            acceptSuggestion(suggestion);
          }
        });
      }, adaptationInterval);
    }

    return () => {
      if (adaptationIntervalRef.current) {
        clearInterval(adaptationIntervalRef.current);
      }
    };
  }, [
    enableAutoAdaptation,
    enableNotifications,
    adaptationInterval,
    confidenceThreshold,
    requestAdaptation,
    acceptSuggestion,
    updateState,
  ]);

  // 主题变化监听
  useEffect(() => {
    if (
      lastThemeRef.current &&
      lastThemeRef.current !== theme &&
      enableLearning
    ) {
      // 记录主题使用时间
      const sessionDuration = Date.now() - sessionStartRef.current;
      smartThemeAdapter.recordThemeUsage(
        lastThemeRef.current as SupportedTheme,
        sessionDuration,
        currentActivityRef.current
      );

      // 重置会话开始时间
      sessionStartRef.current = Date.now();
    }

    lastThemeRef.current = theme;
  }, [theme, enableLearning]);

  // 返回状态和操作
  return [
    state,
    {
      requestAdaptation,
      acceptSuggestion,
      rejectSuggestion,
      applyTheme,
      recordActivity,
      updateEnvironment,
      toggleAutoAdaptation,
      toggleLearning,
      setConfidenceThreshold,
      exportUserData,
      importUserData,
      clearAllData,
      getDebugInfo,
    },
  ];
}
