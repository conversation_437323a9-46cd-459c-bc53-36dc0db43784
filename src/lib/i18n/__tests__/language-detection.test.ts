/**
 * 智能语言检测系统测试
 *
 * 测试覆盖：
 * - URL 语言检测
 * - 浏览器语言偏好检测
 * - 用户偏好存储和检索
 * - 时区地理位置推断
 * - 智能检测主函数
 * - 语言代码规范化
 * - 边界条件和错误处理
 */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  type SupportedLocale,
  detectLocaleFromBrowser,
  detectLocaleFromTimezone,
  detectUserLanguage,
  getDetectionStats,
  getLocaleFromURL,
  getUserSavedLocale,
  normalizeLocale,
  saveUserLocalePreference,
} from '../language-detection';

// Mock routing 模块
vi.mock('@/i18n/routing', () => ({
  routing: {
    locales: ['en', 'zh'],
    defaultLocale: 'en',
  },
}));

// Mock localStorage
const mockLocalStorage = {
  store: new Map<string, string>(),
  getItem: vi.fn((key: string) => mockLocalStorage.store.get(key) ?? null),
  setItem: vi.fn((key: string, value: string) => {
    mockLocalStorage.store.set(key, value);
  }),
  removeItem: vi.fn((key: string) => {
    mockLocalStorage.store.delete(key);
  }),
  clear: vi.fn(() => {
    mockLocalStorage.store.clear();
  }),
};

// Mock navigator
const mockNavigator = {
  languages: ['en-US', 'en'],
  language: 'en-US',
};

// Mock Intl.DateTimeFormat
const mockDateTimeFormat = {
  resolvedOptions: vi.fn(() => ({
    timeZone: 'America/New_York',
  })),
};

// 测试辅助函数
function setupTestEnvironment(): void {
  // 重置所有 mock
  vi.clearAllMocks();
  mockLocalStorage.store.clear();

  // 设置全局 mock
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
    writable: true,
  });

  Object.defineProperty(window, 'navigator', {
    value: mockNavigator,
    writable: true,
  });

  Object.defineProperty(window, 'Intl', {
    value: {
      DateTimeFormat: vi.fn(() => mockDateTimeFormat),
    },
    writable: true,
  });
}

describe('智能语言检测系统', () => {
  beforeEach(setupTestEnvironment);

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getLocaleFromURL', () => {
    it('应该从有效的 URL 路径中提取语言代码', () => {
      expect(getLocaleFromURL('/en/about')).toBe('en');
      expect(getLocaleFromURL('/zh/contact')).toBe('zh');
      expect(getLocaleFromURL('/en')).toBe('en');
      expect(getLocaleFromURL('/zh')).toBe('zh');
    });

    it('应该处理无效的 URL 路径', () => {
      expect(getLocaleFromURL('/invalid/path')).toBeNull();
      expect(getLocaleFromURL('/fr/about')).toBeNull();
      expect(getLocaleFromURL('/')).toBeNull();
      expect(getLocaleFromURL('')).toBeNull();
    });

    it('应该处理复杂的 URL 路径', () => {
      expect(getLocaleFromURL('/en/products/category/item')).toBe('en');
      expect(getLocaleFromURL('/zh/blog/2024/article')).toBe('zh');
    });
  });

  describe('用户偏好存储', () => {
    it('应该保存和检索用户语言偏好', () => {
      const locale: SupportedLocale = 'zh';

      saveUserLocalePreference(locale, 'manual');
      const retrieved = getUserSavedLocale();

      expect(retrieved).toBe(locale);
      expect(mockLocalStorage.setItem).toHaveBeenCalled();
    });

    it('应该处理过期的用户偏好', () => {
      const expiredPreference = {
        locale: 'zh' as SupportedLocale,
        timestamp: Date.now() - 31 * 24 * 60 * 60 * 1000, // 31天前
        source: 'manual',
      };

      mockLocalStorage.store.set(
        'tucsenberg-language-preference',
        JSON.stringify(expiredPreference)
      );

      const retrieved = getUserSavedLocale();
      expect(retrieved).toBeNull();
      expect(mockLocalStorage.removeItem).toHaveBeenCalled();
    });

    it('应该处理无效的存储数据', () => {
      mockLocalStorage.store.set(
        'tucsenberg-language-preference',
        'invalid-json'
      );

      const retrieved = getUserSavedLocale();
      expect(retrieved).toBeNull();
    });

    it('应该验证语言是否仍然支持', () => {
      const invalidPreference = {
        locale: 'fr' as SupportedLocale, // 不支持的语言
        timestamp: Date.now(),
        source: 'manual',
      };

      mockLocalStorage.store.set(
        'tucsenberg-language-preference',
        JSON.stringify(invalidPreference)
      );

      const retrieved = getUserSavedLocale();
      expect(retrieved).toBeNull();
    });
  });

  describe('detectLocaleFromBrowser', () => {
    it('应该从浏览器语言设置中检测语言', () => {
      mockNavigator.languages = ['zh-CN', 'zh', 'en'];
      mockNavigator.language = 'zh-CN';

      const detected = detectLocaleFromBrowser();
      expect(detected).toBe('zh');
    });

    it('应该处理英文语言变体', () => {
      mockNavigator.languages = ['en-GB', 'en-US'];
      mockNavigator.language = 'en-GB';

      const detected = detectLocaleFromBrowser();
      expect(detected).toBe('en');
    });

    it('应该处理不支持的语言', () => {
      mockNavigator.languages = ['fr-FR', 'de-DE'];
      mockNavigator.language = 'fr-FR';

      const detected = detectLocaleFromBrowser();
      expect(detected).toBeNull();
    });

    it('应该处理语言代码匹配', () => {
      mockNavigator.languages = ['zh-TW', 'en-AU'];
      mockNavigator.language = 'zh-TW';

      const detected = detectLocaleFromBrowser();
      expect(detected).toBe('zh');
    });
  });

  describe('detectLocaleFromTimezone', () => {
    it('应该从中国时区检测中文', () => {
      mockDateTimeFormat.resolvedOptions.mockReturnValue({
        timeZone: 'Asia/Shanghai',
      });

      const detected = detectLocaleFromTimezone();
      expect(detected).toBe('zh');
    });

    it('应该从香港时区检测中文', () => {
      mockDateTimeFormat.resolvedOptions.mockReturnValue({
        timeZone: 'Asia/Hong_Kong',
      });

      const detected = detectLocaleFromTimezone();
      expect(detected).toBe('zh');
    });

    it('应该从其他时区检测英文', () => {
      mockDateTimeFormat.resolvedOptions.mockReturnValue({
        timeZone: 'America/New_York',
      });

      const detected = detectLocaleFromTimezone();
      expect(detected).toBe('en');
    });

    it('应该处理时区检测错误', () => {
      mockDateTimeFormat.resolvedOptions.mockImplementation(() => {
        throw new Error('Timezone error');
      });

      const detected = detectLocaleFromTimezone();
      expect(detected).toBeNull();
    });
  });

  describe('detectUserLanguage', () => {
    it('应该优先使用 URL 中的语言', () => {
      const result = detectUserLanguage('/zh/about');

      expect(result.locale).toBe('zh');
      expect(result.source).toBe('url');
      expect(result.confidence).toBe(1.0);
    });

    it('应该使用保存的用户偏好', () => {
      saveUserLocalePreference('zh', 'manual');

      const result = detectUserLanguage('/about');

      expect(result.locale).toBe('zh');
      expect(result.source).toBe('saved');
      expect(result.confidence).toBe(0.9);
    });

    it('应该使用浏览器语言设置', () => {
      mockNavigator.languages = ['zh-CN'];

      const result = detectUserLanguage('/about');

      expect(result.locale).toBe('zh');
      expect(result.source).toBe('browser');
      expect(result.confidence).toBe(0.8);
    });

    it('应该使用时区地理位置推断', () => {
      mockNavigator.languages = ['fr-FR']; // 不支持的语言
      mockDateTimeFormat.resolvedOptions.mockReturnValue({
        timeZone: 'Asia/Shanghai',
      });

      const result = detectUserLanguage('/about');

      expect(result.locale).toBe('zh');
      expect(result.source).toBe('geo');
      expect(result.confidence).toBe(0.6);
    });

    it('应该回退到默认语言', () => {
      mockNavigator.languages = ['fr-FR']; // 不支持的语言
      mockDateTimeFormat.resolvedOptions.mockReturnValue({
        timeZone: 'Europe/Paris', // 不匹配的时区
      });

      const result = detectUserLanguage('/about');

      expect(result.locale).toBe('en'); // 默认语言
      expect(result.source).toBe('geo'); // 时区检测会返回 'en'，所以来源是 'geo'
      expect(result.confidence).toBe(0.6); // 地理位置推断的置信度
      expect(result.fallback).toBe(undefined); // 不是真正的回退
    });

    it('应该在所有检测都失败时使用默认语言', () => {
      mockNavigator.languages = ['fr-FR']; // 不支持的语言
      // 模拟时区检测失败
      mockDateTimeFormat.resolvedOptions.mockImplementation(() => {
        throw new Error('Timezone error');
      });

      const result = detectUserLanguage('/about');

      expect(result.locale).toBe('en'); // 默认语言
      expect(result.source).toBe('default');
      expect(result.confidence).toBe(0.5);
      expect(result.fallback).toBe(true);
    });
  });

  describe('normalizeLocale', () => {
    it('应该规范化中文语言代码', () => {
      expect(normalizeLocale('zh')).toBe('zh');
      expect(normalizeLocale('zh-CN')).toBe('zh');
      expect(normalizeLocale('zh-Hans')).toBe('zh');
      expect(normalizeLocale('ZH-TW')).toBe('zh'); // 大小写不敏感
    });

    it('应该规范化英文语言代码', () => {
      expect(normalizeLocale('en')).toBe('en');
      expect(normalizeLocale('en-US')).toBe('en');
      expect(normalizeLocale('EN-GB')).toBe('en'); // 大小写不敏感
    });

    it('应该处理不支持的语言代码', () => {
      expect(normalizeLocale('fr')).toBeNull();
      expect(normalizeLocale('de-DE')).toBeNull();
      expect(normalizeLocale('invalid')).toBeNull();
    });
  });

  describe('getDetectionStats', () => {
    it('应该返回空统计信息（无历史记录）', () => {
      const stats = getDetectionStats();

      expect(stats.totalDetections).toBe(0);
      expect(stats.sourceDistribution).toEqual({});
      expect(stats.localeDistribution).toEqual({});
    });

    it('应该返回正确的统计信息', () => {
      // 模拟一些检测历史
      const history = [
        {
          locale: 'zh',
          source: 'browser',
          confidence: 0.8,
          timestamp: Date.now(),
        },
        { locale: 'en', source: 'url', confidence: 1.0, timestamp: Date.now() },
        {
          locale: 'zh',
          source: 'saved',
          confidence: 0.9,
          timestamp: Date.now(),
        },
      ];

      mockLocalStorage.store.set(
        'tucsenberg-language-history',
        JSON.stringify(history)
      );

      const stats = getDetectionStats();

      expect(stats.totalDetections).toBe(3);
      expect(stats.sourceDistribution).toEqual({
        browser: 1,
        url: 1,
        saved: 1,
      });
      expect(stats.localeDistribution).toEqual({
        zh: 2,
        en: 1,
      });
    });

    it('应该处理无效的历史数据', () => {
      mockLocalStorage.store.set('tucsenberg-language-history', 'invalid-json');

      const stats = getDetectionStats();

      expect(stats.totalDetections).toBe(0);
      expect(stats.sourceDistribution).toEqual({});
      expect(stats.localeDistribution).toEqual({});
    });
  });
});
