'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to detect user's preference for reduced motion
 *
 * This hook respects the user's system preference for reduced motion,
 * which is crucial for accessibility compliance (WCAG 2.3.3).
 *
 * Users with vestibular disorders may experience discomfort or nausea
 * from animations, so this hook allows us to conditionally disable
 * animations when the user has set `prefers-reduced-motion: reduce`.
 *
 * @returns {boolean} true if user prefers reduced motion, false otherwise
 *
 * @example
 * ```tsx
 * const prefersReducedMotion = usePrefersReducedMotion();
 *
 * <motion.div
 *   animate={prefersReducedMotion ? undefined : 'visible'}
 *   variants={prefersReducedMotion ? {} : animationVariants}
 * >
 *   Content
 * </motion.div>
 * ```
 */
export function usePrefersReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }

    // Create media query to detect reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    // Set initial value
    setPrefersReducedMotion(mediaQuery.matches);

    // Create listener for changes
    const listener = (event: MediaQueryListEvent): void => {
      setPrefersReducedMotion(event.matches);
    };

    // Add event listener
    mediaQuery.addEventListener('change', listener);

    // Cleanup function
    return () => {
      mediaQuery.removeEventListener('change', listener);
    };
  }, []);

  return prefersReducedMotion;
}
