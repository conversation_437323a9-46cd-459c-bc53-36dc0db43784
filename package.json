{"name": "tucsenberg-web-nextjs15", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:check": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "format:check": "prettier --check .", "format:fix": "prettier --write .", "format:lint": "pnpm format:fix && pnpm lint:fix", "prepare": "lefthook install", "commitlint": "commitlint --edit"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.9", "geist": "^1.4.2", "lucide-react": "^0.526.0", "next": "15.4.1", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.10"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@eslint/eslintrc": "^3", "@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "lefthook": "1.11.14", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.8", "tailwindcss": "^4", "typescript": "^5"}}