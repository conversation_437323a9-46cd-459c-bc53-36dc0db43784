#!/usr/bin/env node

/**
 * 安全的 ESLint 批量修复脚本
 * 只修复最安全、最明确的错误类型
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🛡️  安全批量修复 ESLint 错误...\n');

// 获取初始错误数量
function getErrorCount() {
  try {
    const result = execSync('pnpm lint 2>&1 | grep -c "Error:" || echo "0"', { 
      encoding: 'utf-8' 
    });
    return parseInt(result.trim());
  } catch (error) {
    return -1;
  }
}

// 获取所有 TypeScript 文件
function getAllTSFiles() {
  const files = [];
  
  function scanDir(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  }
  
  scanDir('src');
  return files;
}

// 1. 修复 console 语句 (最安全)
function fixConsoleStatements() {
  console.log('📝 修复 console 语句...');
  let fixed = 0;
  
  const files = getAllTSFiles();
  
  for (const file of files) {
    let content = fs.readFileSync(file, 'utf-8');
    const originalContent = content;
    
    // 只包装简单的 console 语句，避免复杂情况
    const lines = content.split('\n');
    const newLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();
      
      // 检查是否是简单的 console 语句
      if (trimmed.match(/^console\.(log|warn|error|info|debug)\([^)]*\);?$/) && 
          !line.includes('process.env.NODE_ENV')) {
        
        const indent = line.match(/^(\s*)/)[1];
        newLines.push(`${indent}if (process.env.NODE_ENV === 'development') {`);
        newLines.push(`${indent}  ${trimmed}`);
        newLines.push(`${indent}}`);
        fixed++;
      } else {
        newLines.push(line);
      }
    }
    
    const newContent = newLines.join('\n');
    if (newContent !== originalContent) {
      fs.writeFileSync(file, newContent);
      console.log(`  ✅ 修复了 ${file}`);
    }
  }
  
  console.log(`✅ 修复了 ${fixed} 个 console 语句\n`);
  return fixed;
}

// 2. 修复简单的 nullish coalescing (安全)
function fixSimpleNullishCoalescing() {
  console.log('🔄 修复简单的 nullish coalescing...');
  let fixed = 0;
  
  const files = getAllTSFiles();
  
  for (const file of files) {
    let content = fs.readFileSync(file, 'utf-8');
    const originalContent = content;
    
    // 只替换明显安全的情况
    const safeReplacements = [
      [/(\w+)\s*\|\|\s*''/g, "$1 ?? ''"],
      [/(\w+)\s*\|\|\s*""/g, '$1 ?? ""'],
      [/(\w+)\s*\|\|\s*0\b/g, '$1 ?? 0'],
      [/(\w+)\s*\|\|\s*\[\]/g, '$1 ?? []'],
      [/(\w+)\s*\|\|\s*\{\}/g, '$1 ?? {}']
    ];
    
    for (const [pattern, replacement] of safeReplacements) {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, replacement);
        fixed += matches.length;
      }
    }
    
    if (content !== originalContent) {
      fs.writeFileSync(file, content);
      console.log(`  ✅ 修复了 ${file}`);
    }
  }
  
  console.log(`✅ 修复了 ${fixed} 个 nullish coalescing\n`);
  return fixed;
}

// 3. 运行 ESLint 自动修复
function runESLintAutoFix() {
  console.log('🔧 运行 ESLint 自动修复...');
  
  try {
    execSync('pnpm lint --fix', { stdio: 'pipe' });
    console.log('✅ ESLint 自动修复完成\n');
    return true;
  } catch (error) {
    console.log('⚠️  ESLint 自动修复遇到一些问题，继续...\n');
    return false;
  }
}

// 主函数
async function main() {
  const startTime = Date.now();
  
  console.log('📊 获取初始错误数量...');
  const initialErrors = getErrorCount();
  console.log(`初始错误数量: ${initialErrors}\n`);
  
  let totalManualFixes = 0;
  
  // 执行安全的批量修复
  totalManualFixes += fixConsoleStatements();
  totalManualFixes += fixSimpleNullishCoalescing();
  
  // 运行 ESLint 自动修复
  runESLintAutoFix();
  
  // 检查最终结果
  console.log('🔍 检查最终错误数量...');
  const finalErrors = getErrorCount();
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log('\n📊 修复结果统计:');
  console.log(`   初始错误: ${initialErrors}`);
  console.log(`   最终错误: ${finalErrors}`);
  console.log(`   总共修复: ${initialErrors - finalErrors}`);
  console.log(`   手动修复: ${totalManualFixes}`);
  console.log(`   自动修复: ${(initialErrors - finalErrors) - totalManualFixes}`);
  console.log(`   修复率: ${(((initialErrors - finalErrors) / initialErrors) * 100).toFixed(1)}%`);
  console.log(`   耗时: ${duration}秒`);
  
  if (finalErrors === 0) {
    console.log('\n🎉 所有错误已修复！项目达到企业级代码质量标准！');
  } else {
    console.log(`\n📝 还需要手动修复 ${finalErrors} 个错误`);
    console.log('\n💡 剩余错误类型分析:');
    try {
      execSync('pnpm lint 2>&1 | grep "Error:" | sed "s/.*Error: //" | sort | uniq -c | sort -nr | head -5', { 
        stdio: 'inherit' 
      });
    } catch (e) {
      console.log('无法显示错误分布');
    }
    
    console.log('\n🎯 建议的下一步:');
    console.log('1. 手动修复类型安全问题 (any 类型)');
    console.log('2. 处理安全相关错误 (对象注入)');
    console.log('3. 重构长函数 (函数长度违规)');
    console.log('4. 添加缺失的返回类型注解');
  }
}

// 运行脚本
main().catch(console.error);
