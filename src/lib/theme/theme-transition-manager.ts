/**
 * 主题过渡动画管理器
 *
 * 功能特性：
 * - 平滑的主题切换动画
 * - 颜色过渡和渐变效果
 * - 动画时序管理和控制
 * - 性能优化和硬件加速
 * - 用户偏好和无障碍支持
 * - 自定义动画配置
 */

// 过渡动画类型
export type TransitionType =
  | 'fade' // 淡入淡出
  | 'slide' // 滑动
  | 'scale' // 缩放
  | 'flip' // 翻转
  | 'dissolve' // 溶解
  | 'morph' // 变形
  | 'none'; // 无动画

// 过渡方向
export type TransitionDirection = 'up' | 'down' | 'left' | 'right' | 'center';

// 缓动函数类型
export type EasingFunction =
  | 'linear'
  | 'ease'
  | 'ease-in'
  | 'ease-out'
  | 'ease-in-out'
  | 'cubic-bezier'
  | 'spring'
  | 'bounce';

// 过渡配置
export interface TransitionConfig {
  type: TransitionType;
  duration: number; // 动画持续时间（毫秒）
  delay: number; // 延迟时间（毫秒）
  easing: EasingFunction; // 缓动函数
  direction?: TransitionDirection; // 过渡方向
  stagger?: number; // 交错动画延迟
  customEasing?: string; // 自定义贝塞尔曲线
  respectMotionPreference: boolean; // 尊重用户动画偏好
}

// 动画状态
export interface AnimationState {
  isAnimating: boolean;
  currentAnimation: string | null;
  progress: number; // 动画进度 (0-1)
  startTime: number;
  endTime: number;
  fromTheme: string;
  toTheme: string;
}

// 颜色过渡信息
export interface ColorTransition {
  property: string; // CSS 属性名
  fromColor: string; // 起始颜色
  toColor: string; // 目标颜色
  currentColor: string; // 当前颜色
  element: HTMLElement; // 目标元素
}

// 动画事件
export interface TransitionEvent {
  type: 'start' | 'progress' | 'complete' | 'cancel';
  animationId: string;
  progress?: number;
  fromTheme: string;
  toTheme: string;
  timestamp: number;
}

// 性能监控数据
export interface PerformanceMetrics {
  frameRate: number; // 帧率
  droppedFrames: number; // 丢帧数
  animationDuration: number; // 实际动画时长
  memoryUsage: number; // 内存使用量
  cpuUsage: number; // CPU 使用率
}

/**
 * 主题过渡动画管理器类
 */
export class ThemeTransitionManager {
  private config: TransitionConfig;
  private animationState: AnimationState;
  private colorTransitions: Map<string, ColorTransition> = new Map();
  private eventListeners: Map<string, ((event: TransitionEvent) => void)[]> =
    new Map();
  private performanceObserver: PerformanceObserver | null = null;
  private animationFrame: number | null = null;
  private isReducedMotion: boolean = false;

  constructor(config: Partial<TransitionConfig> = {}) {
    // 默认配置
    this.config = {
      type: 'fade',
      duration: 300,
      delay: 0,
      easing: 'ease-out',
      direction: 'center',
      stagger: 50,
      respectMotionPreference: true,
      ...config,
    };

    // 初始化动画状态
    this.animationState = {
      isAnimating: false,
      currentAnimation: null,
      progress: 0,
      startTime: 0,
      endTime: 0,
      fromTheme: '',
      toTheme: '',
    };

    // 检测用户动画偏好
    this.detectMotionPreference();

    // 初始化性能监控
    this.initPerformanceMonitoring();

    // 监听媒体查询变化
    this.setupMediaQueryListeners();
  }

  /**
   * 检测用户动画偏好
   */
  private detectMotionPreference(): void {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    this.isReducedMotion = mediaQuery.matches;
  }

  /**
   * 设置媒体查询监听器
   */
  private setupMediaQueryListeners(): void {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', (e) => {
      this.isReducedMotion = e.matches;
      this.detectMotionPreference();
    });
  }

  /**
   * 初始化性能监控
   */
  private initPerformanceMonitoring(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window))
      return;

    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (
            entry.entryType === 'measure' &&
            entry.name.startsWith('theme-transition')
          ) {
            this.emitEvent({
              type: 'progress',
              animationId: entry.name,
              fromTheme: this.animationState.fromTheme,
              toTheme: this.animationState.toTheme,
              timestamp: Date.now(),
            });
          }
        });
      });

      this.performanceObserver.observe({
        entryTypes: ['measure', 'navigation'],
      });
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }

  /**
   * 开始主题过渡动画
   */
  async startTransition(fromTheme: string, toTheme: string): Promise<void> {
    // 如果已在动画中，先取消当前动画
    if (this.animationState.isAnimating) {
      this.cancelTransition();
    }

    // 如果用户偏好无动画或配置为无动画，直接完成
    if (
      (this.isReducedMotion && this.config.respectMotionPreference) ||
      this.config.type === 'none'
    ) {
      this.completeTransitionImmediately(fromTheme, toTheme);
      return;
    }

    const animationId = `theme-transition-${Date.now()}`;
    const startTime = performance.now();

    // 更新动画状态
    this.animationState = {
      isAnimating: true,
      currentAnimation: animationId,
      progress: 0,
      startTime,
      endTime: startTime + this.config.duration + this.config.delay,
      fromTheme,
      toTheme,
    };

    // 发出开始事件
    this.emitEvent({
      type: 'start',
      animationId,
      fromTheme,
      toTheme,
      timestamp: Date.now(),
    });

    // 准备颜色过渡
    await this.prepareColorTransitions(fromTheme, toTheme);

    // 开始动画循环
    this.startAnimationLoop(animationId);
  }

  /**
   * 立即完成过渡（无动画）
   */
  private completeTransitionImmediately(
    fromTheme: string,
    toTheme: string
  ): void {
    const animationId = `theme-transition-immediate-${Date.now()}`;
    const timestamp = Date.now();

    // 设置动画状态
    this.animationState = {
      isAnimating: false,
      currentAnimation: null,
      progress: 1,
      startTime: timestamp,
      endTime: timestamp,
      fromTheme,
      toTheme,
    };

    this.emitEvent({
      type: 'start',
      animationId,
      fromTheme,
      toTheme,
      timestamp,
    });

    this.emitEvent({
      type: 'complete',
      animationId,
      fromTheme,
      toTheme,
      timestamp,
    });
  }

  /**
   * 准备颜色过渡
   */
  private async prepareColorTransitions(
    fromTheme: string,
    toTheme: string
  ): Promise<void> {
    if (typeof window === 'undefined') return;

    this.colorTransitions.clear();

    // 获取需要过渡的 CSS 变量
    const rootElement = document.documentElement;
    const computedStyle = getComputedStyle(rootElement);

    // 定义需要过渡的 CSS 变量
    const transitionProperties = [
      '--background',
      '--foreground',
      '--primary',
      '--primary-foreground',
      '--secondary',
      '--secondary-foreground',
      '--muted',
      '--muted-foreground',
      '--accent',
      '--accent-foreground',
      '--destructive',
      '--destructive-foreground',
      '--border',
      '--input',
      '--ring',
      '--card',
      '--card-foreground',
      '--popover',
      '--popover-foreground',
    ];

    // 临时切换主题以获取目标颜色
    const originalTheme =
      rootElement.getAttribute('data-theme') || rootElement.className;

    // 应用目标主题
    if (toTheme === 'dark') {
      rootElement.classList.add('dark');
    } else {
      rootElement.classList.remove('dark');
    }

    // 获取目标颜色
    const targetStyle = getComputedStyle(rootElement);

    // 恢复原始主题
    if (fromTheme === 'dark') {
      rootElement.classList.add('dark');
    } else {
      rootElement.classList.remove('dark');
    }

    // 创建颜色过渡对象
    transitionProperties.forEach((property) => {
      const fromColor = computedStyle.getPropertyValue(property).trim();
      const toColor = targetStyle.getPropertyValue(property).trim();

      if (fromColor && toColor && fromColor !== toColor) {
        this.colorTransitions.set(property, {
          property,
          fromColor,
          toColor,
          currentColor: fromColor,
          element: rootElement,
        });
      }
    });
  }

  /**
   * 开始动画循环
   */
  private startAnimationLoop(animationId: string): void {
    const animate = (currentTime: number) => {
      if (
        !this.animationState.isAnimating ||
        this.animationState.currentAnimation !== animationId
      ) {
        return;
      }

      const elapsed =
        currentTime - this.animationState.startTime - this.config.delay;
      const progress = Math.max(0, Math.min(1, elapsed / this.config.duration));

      // 应用缓动函数
      const easedProgress = this.applyEasing(progress);

      // 更新动画状态
      this.animationState.progress = progress;

      // 应用颜色过渡
      this.applyColorTransitions(easedProgress);

      // 应用过渡效果
      this.applyTransitionEffect(easedProgress);

      // 发出进度事件
      this.emitEvent({
        type: 'progress',
        animationId,
        progress,
        fromTheme: this.animationState.fromTheme,
        toTheme: this.animationState.toTheme,
        timestamp: Date.now(),
      });

      // 检查动画是否完成
      if (progress >= 1) {
        this.completeTransition(animationId);
      } else {
        this.animationFrame = requestAnimationFrame(animate);
      }
    };

    // 延迟开始动画
    setTimeout(() => {
      this.animationFrame = requestAnimationFrame(animate);
    }, this.config.delay);
  }

  /**
   * 应用缓动函数
   */
  private applyEasing(progress: number): number {
    switch (this.config.easing) {
      case 'linear':
        return progress;
      case 'ease':
        return this.cubicBezier(0.25, 0.1, 0.25, 1, progress);
      case 'ease-in':
        return this.cubicBezier(0.42, 0, 1, 1, progress);
      case 'ease-out':
        return this.cubicBezier(0, 0, 0.58, 1, progress);
      case 'ease-in-out':
        return this.cubicBezier(0.42, 0, 0.58, 1, progress);
      case 'spring':
        return this.springEasing(progress);
      case 'bounce':
        return this.bounceEasing(progress);
      case 'cubic-bezier':
        if (this.config.customEasing) {
          const values = this.config.customEasing.match(/[\d.]+/g);
          if (
            values &&
            values.length === 4 &&
            values.every((v) => v !== undefined)
          ) {
            return this.cubicBezier(
              parseFloat(values[0]!),
              parseFloat(values[1]!),
              parseFloat(values[2]!),
              parseFloat(values[3]!),
              progress
            );
          }
        }
        return progress;
      default:
        return progress;
    }
  }

  /**
   * 三次贝塞尔曲线缓动
   */
  private cubicBezier(
    x1: number,
    y1: number,
    x2: number,
    y2: number,
    t: number
  ): number {
    // 简化的三次贝塞尔曲线实现
    const cx = 3 * x1;
    const bx = 3 * (x2 - x1) - cx;
    const ax = 1 - cx - bx;

    const cy = 3 * y1;
    const by = 3 * (y2 - y1) - cy;
    const ay = 1 - cy - by;

    const sampleCurveX = (t: number) => ((ax * t + bx) * t + cx) * t;
    const sampleCurveY = (t: number) => ((ay * t + by) * t + cy) * t;

    // 使用牛顿法求解
    let x = t;
    for (let i = 0; i < 8; i++) {
      const currentX = sampleCurveX(x) - t;
      if (Math.abs(currentX) < 0.000001) break;
      const currentSlope = (3 * ax * x + 2 * bx) * x + cx;
      if (Math.abs(currentSlope) < 0.000001) break;
      x -= currentX / currentSlope;
    }

    return sampleCurveY(x);
  }

  /**
   * 弹簧缓动
   */
  private springEasing(t: number): number {
    return 1 - Math.cos(t * Math.PI * 0.5);
  }

  /**
   * 弹跳缓动
   */
  private bounceEasing(t: number): number {
    if (t < 1 / 2.75) {
      return 7.5625 * t * t;
    } else if (t < 2 / 2.75) {
      return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    } else if (t < 2.5 / 2.75) {
      return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    } else {
      return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
    }
  }

  /**
   * 应用颜色过渡
   */
  private applyColorTransitions(progress: number): void {
    this.colorTransitions.forEach((transition) => {
      const interpolatedColor = this.interpolateColor(
        transition.fromColor,
        transition.toColor,
        progress
      );

      transition.currentColor = interpolatedColor;
      transition.element.style.setProperty(
        transition.property,
        interpolatedColor
      );
    });
  }

  /**
   * 颜色插值
   */
  private interpolateColor(
    fromColor: string,
    toColor: string,
    progress: number
  ): string {
    // 解析 HSL 颜色
    const fromHsl = this.parseHslColor(fromColor);
    const toHsl = this.parseHslColor(toColor);

    if (fromHsl && toHsl) {
      const h = this.interpolateValue(fromHsl.h, toHsl.h, progress);
      const s = this.interpolateValue(fromHsl.s, toHsl.s, progress);
      const l = this.interpolateValue(fromHsl.l, toHsl.l, progress);
      return `hsl(${h}, ${s}%, ${l}%)`;
    }

    // 回退到直接切换
    return progress < 0.5 ? fromColor : toColor;
  }

  /**
   * 解析 HSL 颜色
   */
  private parseHslColor(
    color: string
  ): { h: number; s: number; l: number } | null {
    const hslMatch = color.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
    if (hslMatch && hslMatch[1] && hslMatch[2] && hslMatch[3]) {
      return {
        h: parseInt(hslMatch[1]),
        s: parseInt(hslMatch[2]),
        l: parseInt(hslMatch[3]),
      };
    }
    return null;
  }

  /**
   * 数值插值
   */
  private interpolateValue(from: number, to: number, progress: number): number {
    return from + (to - from) * progress;
  }

  /**
   * 应用过渡效果
   */
  private applyTransitionEffect(progress: number): void {
    if (typeof window === 'undefined') return;

    const rootElement = document.documentElement;

    switch (this.config.type) {
      case 'fade':
        this.applyFadeEffect(rootElement, progress);
        break;
      case 'slide':
        this.applySlideEffect(rootElement, progress);
        break;
      case 'scale':
        this.applyScaleEffect(rootElement, progress);
        break;
      case 'flip':
        this.applyFlipEffect(rootElement, progress);
        break;
      case 'dissolve':
        this.applyDissolveEffect(rootElement, progress);
        break;
      case 'morph':
        this.applyMorphEffect(rootElement, progress);
        break;
      default:
        break;
    }
  }

  /**
   * 淡入淡出效果
   */
  private applyFadeEffect(element: HTMLElement, progress: number): void {
    const opacity = 1 - Math.abs(progress - 0.5) * 2;
    element.style.setProperty('--theme-transition-opacity', opacity.toString());
  }

  /**
   * 滑动效果
   */
  private applySlideEffect(element: HTMLElement, progress: number): void {
    const translateX = (progress - 0.5) * 100;
    element.style.setProperty(
      '--theme-transition-translate-x',
      `${translateX}%`
    );
  }

  /**
   * 缩放效果
   */
  private applyScaleEffect(element: HTMLElement, progress: number): void {
    const scale = 0.95 + Math.abs(progress - 0.5) * 0.1;
    element.style.setProperty('--theme-transition-scale', scale.toString());
  }

  /**
   * 翻转效果
   */
  private applyFlipEffect(element: HTMLElement, progress: number): void {
    const rotateY = progress * 180;
    element.style.setProperty('--theme-transition-rotate-y', `${rotateY}deg`);
  }

  /**
   * 溶解效果
   */
  private applyDissolveEffect(element: HTMLElement, progress: number): void {
    const blur = Math.sin(progress * Math.PI) * 2;
    element.style.setProperty('--theme-transition-blur', `${blur}px`);
  }

  /**
   * 变形效果
   */
  private applyMorphEffect(element: HTMLElement, progress: number): void {
    const skewX = Math.sin(progress * Math.PI) * 5;
    element.style.setProperty('--theme-transition-skew-x', `${skewX}deg`);
  }

  /**
   * 完成过渡动画
   */
  private completeTransition(animationId: string): void {
    // 清理动画状态
    this.animationState.isAnimating = false;
    this.animationState.currentAnimation = null;
    this.animationState.progress = 1;

    // 清理动画帧
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    // 清理过渡效果
    this.cleanupTransitionEffects();

    // 发出完成事件
    this.emitEvent({
      type: 'complete',
      animationId,
      fromTheme: this.animationState.fromTheme,
      toTheme: this.animationState.toTheme,
      timestamp: Date.now(),
    });
  }

  /**
   * 取消过渡动画
   */
  cancelTransition(): void {
    if (!this.animationState.isAnimating) return;

    const animationId = this.animationState.currentAnimation || 'unknown';

    // 清理动画状态
    this.animationState.isAnimating = false;
    this.animationState.currentAnimation = null;

    // 清理动画帧
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    // 清理过渡效果
    this.cleanupTransitionEffects();

    // 发出取消事件
    this.emitEvent({
      type: 'cancel',
      animationId,
      fromTheme: this.animationState.fromTheme,
      toTheme: this.animationState.toTheme,
      timestamp: Date.now(),
    });
  }

  /**
   * 清理过渡效果
   */
  private cleanupTransitionEffects(): void {
    if (typeof window === 'undefined') return;

    const rootElement = document.documentElement;

    // 清理 CSS 变量
    rootElement.style.removeProperty('--theme-transition-opacity');
    rootElement.style.removeProperty('--theme-transition-translate-x');
    rootElement.style.removeProperty('--theme-transition-scale');
    rootElement.style.removeProperty('--theme-transition-rotate-y');
    rootElement.style.removeProperty('--theme-transition-blur');
    rootElement.style.removeProperty('--theme-transition-skew-x');
  }

  /**
   * 发出事件
   */
  private emitEvent(event: TransitionEvent): void {
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach((listener) => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in transition event listener:', error);
      }
    });
  }

  /**
   * 添加事件监听器
   */
  addEventListener(
    type: TransitionEvent['type'],
    listener: (event: TransitionEvent) => void
  ): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(
    type: TransitionEvent['type'],
    listener: (event: TransitionEvent) => void
  ): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TransitionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.detectMotionPreference();
  }

  /**
   * 获取当前配置
   */
  getConfig(): TransitionConfig {
    return { ...this.config };
  }

  /**
   * 获取动画状态
   */
  getAnimationState(): AnimationState {
    return { ...this.animationState };
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics | null {
    if (typeof window === 'undefined' || !('performance' in window))
      return null;

    return {
      frameRate: 60, // 简化实现
      droppedFrames: 0,
      animationDuration: this.config.duration,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      cpuUsage: 0, // 需要更复杂的实现
    };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.cancelTransition();

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    this.eventListeners.clear();
    this.colorTransitions.clear();
  }
}

// 全局实例
export const themeTransitionManager = new ThemeTransitionManager({
  type: 'fade',
  duration: 300,
  delay: 0,
  easing: 'ease-out',
  respectMotionPreference: true,
});
