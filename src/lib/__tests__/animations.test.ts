/**
 * 动画系统测试
 *
 * 测试内容：
 * - 动画配置验证
 * - 预设动画类验证
 * - ScrollAnimationObserver 功能测试
 */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  type AnimationType,
  ScrollAnimationObserver,
  animationConfigs,
  presetAnimations,
} from '../animations';

// Mock IntersectionObserver
const mockObserve = vi.fn();
const mockUnobserve = vi.fn();
const mockDisconnect = vi.fn();

const mockIntersectionObserver = vi
  .fn()
  .mockImplementation((callback, options) => ({
    observe: mockObserve,
    unobserve: mockUnobserve,
    disconnect: mockDisconnect,
    root: null,
    rootMargin: options?.rootMargin || '0px',
    thresholds: Array.isArray(options?.threshold)
      ? options.threshold
      : [options?.threshold || 0],
  }));

// 设置全局 mock
Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: mockIntersectionObserver,
});

Object.defineProperty(global, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: mockIntersectionObserver,
});

describe('动画系统', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // 确保 IntersectionObserver 在每个测试前都正确设置
    Object.defineProperty(global, 'IntersectionObserver', {
      writable: true,
      configurable: true,
      value: mockIntersectionObserver,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('动画配置', () => {
    it('应该包含所有必需的动画类型', () => {
      const expectedAnimations: AnimationType[] = [
        'fadeIn',
        'slideUp',
        'slideDown',
        'slideLeft',
        'slideRight',
        'scaleUp',
        'scaleDown',
        'rotateIn',
        'bounceIn',
        'flipIn',
      ];

      expectedAnimations.forEach((animation) => {
        expect(animationConfigs[animation]).toBeDefined();
        expect(animationConfigs[animation].duration).toBeDefined();
        expect(animationConfigs[animation].easing).toBeDefined();
      });
    });

    it('应该有合理的默认配置', () => {
      const fadeInConfig = animationConfigs.fadeIn;

      expect(fadeInConfig.duration).toBe('duration-700');
      expect(fadeInConfig.easing).toBe('ease-out');
      expect(fadeInConfig.threshold).toBe(0.1);
      expect(fadeInConfig.rootMargin).toBe('0px 0px -50px 0px');
    });
  });

  describe('预设动画类', () => {
    it('应该包含所有预设动画', () => {
      const expectedPresets = [
        'cardHover',
        'buttonHover',
        'linkHover',
        'iconSpin',
        'navHover',
        'themeTransition',
      ];

      expectedPresets.forEach((preset) => {
        expect(presetAnimations[preset]).toBeDefined();
        expect(typeof presetAnimations[preset]).toBe('string');
      });
    });

    it('预设动画应该包含正确的 CSS 类', () => {
      expect(presetAnimations.cardHover).toContain('transition-all');
      expect(presetAnimations.cardHover).toContain('hover:scale-105');

      expect(presetAnimations.buttonHover).toContain('transition-all');
      expect(presetAnimations.buttonHover).toContain('hover:scale-105');

      expect(presetAnimations.themeTransition).toContain('transition-colors');
    });
  });

  describe('ScrollAnimationObserver', () => {
    let observer: ScrollAnimationObserver;
    let mockElement: HTMLElement;
    let mockCallback: ReturnType<typeof vi.fn>;

    beforeEach(() => {
      // 确保在创建 observer 之前 mock 已经设置好
      vi.clearAllMocks();
      mockElement = document.createElement('div');
      mockCallback = vi.fn();
      observer = new ScrollAnimationObserver();
    });

    afterEach(() => {
      // 安全地断开连接
      try {
        observer.disconnect();
      } catch {
        // 忽略测试环境中的错误
      }
    });

    it('应该正确初始化', () => {
      expect(observer).toBeInstanceOf(ScrollAnimationObserver);
    });

    it('应该能够观察元素（基础功能测试）', () => {
      // 测试基础的 observe 方法调用不会抛出错误
      expect(() => {
        observer.observe(mockElement, 'fadeIn', mockCallback);
      }).not.toThrow();
    });

    it('应该能够停止观察元素（基础功能测试）', () => {
      // 测试基础的 unobserve 方法调用不会抛出错误
      expect(() => {
        observer.observe(mockElement, 'fadeIn', mockCallback);
        observer.unobserve(mockElement);
      }).not.toThrow();
    });

    it('应该能够断开所有观察（基础功能测试）', () => {
      // 测试基础的 disconnect 方法调用不会抛出错误
      expect(() => {
        observer.observe(mockElement, 'fadeIn', mockCallback);
        observer.disconnect();
      }).not.toThrow();
    });
  });

  describe('动画类型安全', () => {
    it('所有动画配置键应该匹配 AnimationType', () => {
      const configKeys = Object.keys(animationConfigs) as AnimationType[];
      const expectedTypes: AnimationType[] = [
        'fadeIn',
        'slideUp',
        'slideDown',
        'slideLeft',
        'slideRight',
        'scaleUp',
        'scaleDown',
        'rotateIn',
        'bounceIn',
        'flipIn',
      ];

      expect(configKeys.sort()).toEqual(expectedTypes.sort());
    });
  });
});
