/**
 * 动态翻译加载 React Hook
 *
 * 功能特性：
 * - 按需加载翻译模块
 * - 智能缓存和预加载
 * - 加载状态管理
 * - 错误处理和重试
 * - 性能监控和统计
 */
'use client';

import { useCallback, useEffect, useState } from 'react';
import { useLocale } from 'next-intl';
import {
  type LoadResult,
  type LoadingStats,
  type TranslationData,
  type TranslationModule,
  translationLoader,
} from '@/lib/i18n/dynamic-translation-loader';
import type { SupportedLocale } from '@/lib/i18n/language-detection';

/**
 * 动态翻译加载 React Hook
 *
 * 功能特性：
 * - 按需加载翻译模块
 * - 智能缓存和预加载
 * - 加载状态管理
 * - 错误处理和重试
 * - 性能监控和统计
 */

/**
 * 动态翻译加载 React Hook
 *
 * 功能特性：
 * - 按需加载翻译模块
 * - 智能缓存和预加载
 * - 加载状态管理
 * - 错误处理和重试
 * - 性能监控和统计
 */

/**
 * 动态翻译加载 React Hook
 *
 * 功能特性：
 * - 按需加载翻译模块
 * - 智能缓存和预加载
 * - 加载状态管理
 * - 错误处理和重试
 * - 性能监控和统计
 */

// Hook 返回值接口
export interface DynamicTranslationsHook {
  // 翻译数据
  translations: Record<TranslationModule, TranslationData | null>;

  // 加载状态
  loadingStates: Record<
    TranslationModule,
    'idle' | 'loading' | 'loaded' | 'error'
  >;
  isLoading: boolean;
  hasErrors: boolean;

  // 功能方法
  loadModule: (module: TranslationModule) => Promise<LoadResult>;
  loadModules: (
    modules: TranslationModule[]
  ) => Promise<Record<TranslationModule, LoadResult>>;
  preloadModules: (modules?: TranslationModule[]) => Promise<void>;
  retryFailedModules: () => Promise<void>;

  // 实用工具
  getTranslation: (
    module: TranslationModule,
    key: string,
    fallback?: string
  ) => string;
  isModuleLoaded: (module: TranslationModule) => boolean;
  getLoadingProgress: () => number;

  // 统计和调试
  stats: LoadingStats;
  refreshStats: () => void;
  clearCache: () => void;
}

// Hook 配置选项
export interface DynamicTranslationsOptions {
  // 自动加载的模块
  autoLoadModules?: TranslationModule[];

  // 是否启用预加载
  enablePreloading?: boolean;

  // 预加载的模块
  preloadModules?: TranslationModule[];

  // 是否启用调试模式
  debug?: boolean;

  // 错误重试次数
  maxRetries?: number;
}

// 默认配置
const DEFAULT_OPTIONS: Required<DynamicTranslationsOptions> = {
  autoLoadModules: ['common', 'navigation'],
  enablePreloading: true,
  preloadModules: ['common', 'navigation', 'home', 'errors'],
  debug: false,
  maxRetries: 3,
};

/**
 * 动态翻译加载 Hook
 */
export function useDynamicTranslations(
  options: DynamicTranslationsOptions = {}
): DynamicTranslationsHook {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const currentLocale = useLocale() as SupportedLocale;

  // 状态管理
  const [translations, setTranslations] = useState<
    Record<TranslationModule, TranslationData | null>
  >({} as Record<TranslationModule, TranslationData | null>);
  const [loadingStates, setLoadingStates] = useState<
    Record<TranslationModule, 'idle' | 'loading' | 'loaded' | 'error'>
  >({} as Record<TranslationModule, 'idle' | 'loading' | 'loaded' | 'error'>);
  const [stats, setStats] = useState<LoadingStats>(() =>
    translationLoader.getStats()
  );

  // 计算派生状态
  const isLoading = Object.values(loadingStates).some(
    (state) => state === 'loading'
  );
  const hasErrors = Object.values(loadingStates).some(
    (state) => state === 'error'
  );

  // 更新加载状态
  const updateLoadingState = useCallback(
    (
      module: TranslationModule,
      state: 'idle' | 'loading' | 'loaded' | 'error'
    ) => {
      setLoadingStates((prev) => ({ ...prev, [module]: state }));
    },
    []
  );

  // 更新翻译数据
  const updateTranslation = useCallback(
    (module: TranslationModule, data: TranslationData | null) => {
      setTranslations((prev) => ({ ...prev, [module]: data }));
    },
    []
  );

  // 刷新统计信息
  const refreshStats = useCallback(() => {
    setStats(translationLoader.getStats());
  }, []);

  // 处理加载成功的结果
  const handleLoadSuccess = useCallback(
    (module: TranslationModule, result: LoadResult): void => {
      updateTranslation(module, result.data!);
      updateLoadingState(module, 'loaded');

      if (config.debug) {
        // eslint-disable-next-line no-console
        console.log(`[DynamicTranslations] Module loaded: ${module}`, {
          fromCache: result.fromCache,
          loadTime: result.loadTime,
        });
      }
    },
    [config.debug, updateTranslation, updateLoadingState]
  );

  // 处理加载失败的结果
  const handleLoadFailure = useCallback(
    (module: TranslationModule, result: LoadResult): void => {
      updateLoadingState(module, 'error');

      if (config.debug) {
        // eslint-disable-next-line no-console
        console.error(
          `[DynamicTranslations] Failed to load module: ${module}`,
          result.error
        );
      }
    },
    [config.debug, updateLoadingState]
  );

  // 处理加载异常
  const handleLoadError = useCallback(
    (module: TranslationModule, error: unknown): LoadResult => {
      updateLoadingState(module, 'error');

      if (config.debug) {
        // eslint-disable-next-line no-console
        console.error(
          `[DynamicTranslations] Error loading module: ${module}`,
          error
        );
      }

      return {
        success: false,
        error: error as Error,
        fromCache: false,
        loadTime: 0,
      };
    },
    [config.debug, updateLoadingState]
  );

  // 加载单个模块
  const loadModule = useCallback(
    async (module: TranslationModule): Promise<LoadResult> => {
      if (config.debug) {
        // eslint-disable-next-line no-console
        console.log(`[DynamicTranslations] Loading module: ${module}`);
      }

      updateLoadingState(module, 'loading');

      try {
        const result = await translationLoader.loadModule(
          module,
          currentLocale
        );

        if (
          result.success &&
          result.data !== null &&
          result.data !== undefined
        ) {
          handleLoadSuccess(module, result);
        } else {
          handleLoadFailure(module, result);
        }

        refreshStats();
        return result;
      } catch (error) {
        return handleLoadError(module, error);
      }
    },
    [
      config.debug,
      currentLocale,
      updateLoadingState,
      handleLoadSuccess,
      handleLoadFailure,
      handleLoadError,
      refreshStats,
    ]
  );

  // 处理批量加载结果
  const processBatchResults = useCallback(
    (results: Record<TranslationModule, LoadResult>): void => {
      Object.entries(results).forEach(([module, result]) => {
        const moduleKey = module as TranslationModule;

        if (
          result.success &&
          result.data !== null &&
          result.data !== undefined
        ) {
          updateTranslation(moduleKey, result.data);
          updateLoadingState(moduleKey, 'loaded');
        } else {
          updateLoadingState(moduleKey, 'error');
        }
      });
    },
    [updateTranslation, updateLoadingState]
  );

  // 处理批量加载错误
  const handleBatchError = useCallback(
    (modules: TranslationModule[], error: unknown): void => {
      // 设置所有模块为错误状态
      modules.forEach((module) => updateLoadingState(module, 'error'));

      if (config.debug) {
        // eslint-disable-next-line no-console
        console.error(
          `[DynamicTranslations] Error loading modules:`,
          modules,
          error
        );
      }
    },
    [config.debug, updateLoadingState]
  );

  // 批量加载模块
  const loadModules = useCallback(
    async (
      modules: TranslationModule[]
    ): Promise<Record<TranslationModule, LoadResult>> => {
      if (config.debug) {
        // eslint-disable-next-line no-console
        console.log(`[DynamicTranslations] Loading modules:`, modules);
      }

      // 设置加载状态
      modules.forEach((module) => updateLoadingState(module, 'loading'));

      try {
        const results = await translationLoader.loadModules(
          modules,
          currentLocale
        );

        processBatchResults(results);
        refreshStats();
        return results;
      } catch (error) {
        handleBatchError(modules, error);
        throw error;
      }
    },
    [
      config.debug,
      currentLocale,
      updateLoadingState,
      processBatchResults,
      handleBatchError,
      refreshStats,
    ]
  );

  // 预加载模块
  const preloadModules = useCallback(
    async (modules?: TranslationModule[]): Promise<void> => {
      const modulesToPreload = modules || config.preloadModules;

      if (config.debug) {
        console.log(
          `[DynamicTranslations] Preloading modules:`,
          modulesToPreload
        );
      }

      try {
        await translationLoader.preloadModules(currentLocale);
        refreshStats();
      } catch (error) {
        if (config.debug) {
          console.error(
            `[DynamicTranslations] Error preloading modules:`,
            error
          );
        }
      }
    },
    [config.preloadModules, config.debug, currentLocale, refreshStats]
  );

  // 重试失败的模块
  const retryFailedModules = useCallback(async (): Promise<void> => {
    const failedModules = Object.entries(loadingStates)
      .filter(([, state]) => state === 'error')
      .map(([module]) => module as TranslationModule);

    if (failedModules.length > 0) {
      if (config.debug) {
        console.log(
          `[DynamicTranslations] Retrying failed modules:`,
          failedModules
        );
      }

      await loadModules(failedModules);
    }
  }, [loadingStates, loadModules, config.debug]);

  // 获取翻译文本
  const getTranslation = useCallback(
    (module: TranslationModule, key: string, fallback?: string): string => {
      const moduleData = translations[module];

      if (!moduleData) {
        return fallback || key;
      }

      // 支持嵌套键（如 'hero.title'）
      const keys = key.split('.');
      let value: any = moduleData;

      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          return fallback || key;
        }
      }

      return typeof value === 'string' ? value : fallback || key;
    },
    [translations]
  );

  // 检查模块是否已加载
  const isModuleLoaded = useCallback(
    (module: TranslationModule): boolean => {
      return loadingStates[module] === 'loaded';
    },
    [loadingStates]
  );

  // 获取加载进度
  const getLoadingProgress = useCallback((): number => {
    const totalModules = Object.keys(loadingStates).length;
    if (totalModules === 0) return 0;

    const loadedModules = Object.values(loadingStates).filter(
      (state) => state === 'loaded'
    ).length;
    return (loadedModules / totalModules) * 100;
  }, [loadingStates]);

  // 清除缓存
  const clearCache = useCallback(() => {
    translationLoader.clearCache();
    setTranslations({} as Record<TranslationModule, TranslationData | null>);
    setLoadingStates(
      {} as Record<TranslationModule, 'idle' | 'loading' | 'loaded' | 'error'>
    );
    refreshStats();

    if (config.debug) {
      console.log('[DynamicTranslations] Cache cleared');
    }
  }, [config.debug, refreshStats]);

  // 自动加载模块
  useEffect(() => {
    if (config.autoLoadModules.length > 0) {
      void loadModules(config.autoLoadModules);
    }
  }, [config.autoLoadModules, loadModules]);

  // 预加载模块
  useEffect(() => {
    if (config.enablePreloading) {
      // 延迟预加载，避免阻塞初始渲染
      const timer = setTimeout(() => {
        void preloadModules();
      }, 1000);

      return () => clearTimeout(timer);
    }

    // 如果不启用预加载，返回空的清理函数
    return () => {};
  }, [config.enablePreloading, preloadModules]);

  // 语言变化时清除缓存并重新加载
  useEffect(() => {
    clearCache();

    if (config.autoLoadModules.length > 0) {
      void loadModules(config.autoLoadModules);
    }
  }, [currentLocale]); // 只依赖 currentLocale，避免无限循环

  return {
    // 翻译数据
    translations,

    // 加载状态
    loadingStates,
    isLoading,
    hasErrors,

    // 功能方法
    loadModule,
    loadModules,
    preloadModules,
    retryFailedModules,

    // 实用工具
    getTranslation,
    isModuleLoaded,
    getLoadingProgress,

    // 统计和调试
    stats,
    refreshStats,
    clearCache,
  };
}

/**
 * 简化版动态翻译 Hook（仅提供基础功能）
 */
export function useTranslations(modules: TranslationModule[] = ['common']) {
  return useDynamicTranslations({
    autoLoadModules: modules,
    enablePreloading: false,
    debug: false,
  });
}

/**
 * 调试版动态翻译 Hook（启用详细日志）
 */
export function useTranslationsDebug(
  modules: TranslationModule[] = ['common']
) {
  return useDynamicTranslations({
    autoLoadModules: modules,
    enablePreloading: true,
    debug: true,
  });
}
