/**
 * 动态翻译文件 API 路由
 *
 * 功能特性：
 * - 按模块提供翻译文件
 * - 支持缓存控制和压缩
 * - 错误处理和回退机制
 * - 性能监控和日志记录
 */
import { NextRequest, NextResponse } from 'next/server';
import { type TranslationModule } from '@/lib/i18n/dynamic-translation-loader';
import type { SupportedLocale } from '@/lib/i18n/language-detection';

// 支持的语言和模块
const SUPPORTED_LOCALES: SupportedLocale[] = ['en', 'zh'];
const SUPPORTED_MODULES: TranslationModule[] = [
  'common',
  'navigation',
  'home',
  'about',
  'contact',
  'blog',
  'products',
  'services',
  'forms',
  'errors',
];

// 翻译文件映射（模拟分割后的翻译数据）
// 翻译数据类型定义
interface TranslationData {
  [key: string]: string | TranslationData;
}

const TRANSLATION_MODULES: Record<
  SupportedLocale,
  Record<TranslationModule, TranslationData>
> = {
  en: {
    common: {
      title: 'Tucsenberg Web',
      description: 'Modern B2B Enterprise Website Template',
      loading: 'Loading...',
      error: 'An error occurred',
      retry: 'Retry',
      close: 'Close',
      save: 'Save',
      cancel: 'Cancel',
      submit: 'Submit',
      next: 'Next',
      previous: 'Previous',
    },
    navigation: {
      home: 'Home',
      products: 'Products',
      blog: 'Blog',
      about: 'About',
      services: 'Services',
      contact: 'Contact',
      language: 'Language',
      theme: 'Theme',
      menu: 'Menu',
      close: 'Close',
    },
    home: {
      hero: {
        title: 'Tucsenberg Web',
        subtitle: 'Modern B2B Enterprise Website Template',
        description:
          'Built with Next.js 15, TypeScript, Tailwind CSS, and shadcn/ui. A comprehensive template showcasing modern web development best practices.',
        cta: {
          primary: 'View Components',
          secondary: 'View Source Code',
        },
      },
      techStack: {
        title: 'Technology Stack',
        subtitle:
          'Built with cutting-edge technologies for optimal performance and developer experience',
      },
    },
    about: {
      title: 'About Us',
      subtitle: 'Learn more about our company and mission',
      description: 'We are dedicated to creating exceptional web experiences.',
    },
    contact: {
      title: 'Contact Us',
      subtitle: 'Get in touch with our team',
      form: {
        name: 'Name',
        email: 'Email',
        message: 'Message',
        submit: 'Send Message',
      },
    },
    blog: {
      title: 'Blog',
      subtitle: 'Latest news and insights',
      readMore: 'Read More',
      categories: 'Categories',
      tags: 'Tags',
    },
    products: {
      title: 'Products',
      subtitle: 'Discover our product offerings',
      features: 'Features',
      pricing: 'Pricing',
      demo: 'View Demo',
    },
    services: {
      title: 'Services',
      subtitle: 'Professional services we offer',
      consultation: 'Consultation',
      development: 'Development',
      support: 'Support',
    },
    forms: {
      validation: {
        required: 'This field is required',
        email: 'Please enter a valid email address',
        minLength: 'Minimum {min} characters required',
        maxLength: 'Maximum {max} characters allowed',
      },
      messages: {
        success: 'Form submitted successfully',
        error: 'An error occurred while submitting the form',
        loading: 'Submitting...',
      },
    },
    errors: {
      404: {
        title: 'Page Not Found',
        description: 'The page you are looking for does not exist.',
        action: 'Go Home',
      },
      500: {
        title: 'Internal Server Error',
        description: 'Something went wrong on our end.',
        action: 'Try Again',
      },
      network: {
        title: 'Network Error',
        description: 'Please check your internet connection.',
        action: 'Retry',
      },
    },
  },
  zh: {
    common: {
      title: 'Tucsenberg Web',
      description: '现代化 B2B 企业网站模板',
      loading: '加载中...',
      error: '发生错误',
      retry: '重试',
      close: '关闭',
      save: '保存',
      cancel: '取消',
      submit: '提交',
      next: '下一步',
      previous: '上一步',
    },
    navigation: {
      home: '首页',
      products: '产品',
      blog: '博客',
      about: '关于',
      services: '服务',
      contact: '联系',
      language: '语言',
      theme: '主题',
      menu: '菜单',
      close: '关闭',
    },
    home: {
      hero: {
        title: 'Tucsenberg Web',
        subtitle: '现代化 B2B 企业网站模板',
        description:
          '基于 Next.js 15、TypeScript、Tailwind CSS 和 shadcn/ui 构建。展示现代 Web 开发最佳实践的综合模板。',
        cta: {
          primary: '查看组件',
          secondary: '查看源代码',
        },
      },
      techStack: {
        title: '技术栈',
        subtitle: '采用前沿技术构建，提供最佳性能和开发体验',
      },
    },
    about: {
      title: '关于我们',
      subtitle: '了解更多关于我们公司和使命',
      description: '我们致力于创造卓越的网络体验。',
    },
    contact: {
      title: '联系我们',
      subtitle: '与我们的团队取得联系',
      form: {
        name: '姓名',
        email: '邮箱',
        message: '消息',
        submit: '发送消息',
      },
    },
    blog: {
      title: '博客',
      subtitle: '最新新闻和见解',
      readMore: '阅读更多',
      categories: '分类',
      tags: '标签',
    },
    products: {
      title: '产品',
      subtitle: '发现我们的产品',
      features: '功能',
      pricing: '价格',
      demo: '查看演示',
    },
    services: {
      title: '服务',
      subtitle: '我们提供的专业服务',
      consultation: '咨询',
      development: '开发',
      support: '支持',
    },
    forms: {
      validation: {
        required: '此字段为必填项',
        email: '请输入有效的邮箱地址',
        minLength: '最少需要 {min} 个字符',
        maxLength: '最多允许 {max} 个字符',
      },
      messages: {
        success: '表单提交成功',
        error: '提交表单时发生错误',
        loading: '提交中...',
      },
    },
    errors: {
      404: {
        title: '页面未找到',
        description: '您查找的页面不存在。',
        action: '返回首页',
      },
      500: {
        title: '内部服务器错误',
        description: '我们这边出了点问题。',
        action: '重试',
      },
      network: {
        title: '网络错误',
        description: '请检查您的网络连接。',
        action: '重试',
      },
    },
  },
};

/**
 * 处理翻译文件请求
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ locale: string; module: string }> }
): Promise<NextResponse> {
  try {
    const resolvedParams = await params;
    const { locale, module } = resolvedParams;

    // 验证参数
    if (!SUPPORTED_LOCALES.includes(locale as SupportedLocale)) {
      return NextResponse.json(
        { error: 'Unsupported locale' },
        { status: 400 }
      );
    }

    if (!SUPPORTED_MODULES.includes(module as TranslationModule)) {
      return NextResponse.json(
        { error: 'Unsupported module' },
        { status: 400 }
      );
    }

    // 获取翻译数据
    const localeData = TRANSLATION_MODULES[locale as SupportedLocale];
    const translationData = localeData?.[module as TranslationModule];

    if (translationData === undefined) {
      return NextResponse.json(
        { error: 'Translation not found' },
        { status: 404 }
      );
    }

    // 设置缓存头
    const response = NextResponse.json(translationData);

    // 缓存 1 小时
    response.headers.set(
      'Cache-Control',
      'public, max-age=3600, s-maxage=3600'
    );
    response.headers.set('ETag', `"${locale}-${module}-${Date.now()}"`);

    // 添加调试信息头
    response.headers.set('X-Translation-Locale', locale);
    response.headers.set('X-Translation-Module', module);
    response.headers.set(
      'X-Translation-Size',
      JSON.stringify(translationData).length.toString()
    );

    return response;
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Translation API error:', error);
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * 处理 OPTIONS 请求（CORS 预检）
 */
export function OPTIONS(): NextResponse {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
