import type { ReactElement } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

export function InteractionDemo(): ReactElement {
  const t = useTranslations('home.demo.interactionDemo');

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
        <CardDescription>{t('description')}</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex flex-col gap-4 sm:flex-row'>
          <Button className='flex-1'>{t('buttons.click')}</Button>
          <Button variant='outline' className='flex-1'>
            {t('buttons.hover')}
          </Button>
          <Button variant='secondary' className='flex-1'>
            {t('buttons.focus')}
          </Button>
        </div>
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
          <Input placeholder={t('inputs.focusPlaceholder')} />
          <Input placeholder={t('inputs.hoverPlaceholder')} />
        </div>
      </CardContent>
    </Card>
  );
}
