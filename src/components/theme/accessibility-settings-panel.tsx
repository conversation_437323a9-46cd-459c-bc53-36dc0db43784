/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */
'use client';

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useAccessibilityTheme,
  useSystemAccessibilityPreferences,
} from '@/hooks/use-accessibility-theme';
import type { AccessibilityConfig } from '@/lib/theme/accessibility-theme-manager';

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

/**
 * 无障碍设置面板组件
 *
 * 功能特性：
 * - 高对比度设置
 * - 字体大小调节
 * - 动画减少设置
 * - 色盲友好模式
 * - 系统偏好检测
 * - 快速设置切换
 * - 设置预览功能
 */

// 组件属性接口
export interface AccessibilitySettingsPanelProps {
  className?: string;
  showSystemInfo?: boolean;
  showPreview?: boolean;
  onConfigChange?: (config: AccessibilityConfig) => void;
}

/**
 * 对比度设置组件
 */
function ContrastSettings({
  state,
  actions,
  contrastLevelOptions,
}: {
  state: { isHighContrast: boolean; contrastLevel: string };
  actions: {
    toggleHighContrast: () => void;
    setContrastLevel: (level: 'normal' | 'high' | 'higher' | 'maximum') => void;
  };
  contrastLevelOptions: Array<{ value: string; label: string }>;
}): React.ReactElement {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-medium'>对比度设置</h3>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <Label htmlFor='high-contrast'>启用高对比度</Label>
          <Switch
            id='high-contrast'
            checked={state.isHighContrast}
            onCheckedChange={actions.toggleHighContrast}
          />
        </div>

        <div className='space-y-2'>
          <Label>对比度级别</Label>
          <Select
            value={state.contrastLevel}
            onValueChange={actions.setContrastLevel}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {contrastLevelOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

/**
 * 字体设置组件
 */
function FontSettings({
  state,
  actions,
}: {
  state: { isLargeText: boolean; fontSize: number };
  actions: {
    toggleLargeText: () => void;
    setFontScale: (size: number) => void;
  };
}): React.ReactElement {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-medium'>字体设置</h3>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <Label htmlFor='large-text'>启用大字体</Label>
          <Switch
            id='large-text'
            checked={state.isLargeText}
            onCheckedChange={actions.toggleLargeText}
          />
        </div>

        <div className='space-y-2'>
          <Label>字体缩放: {Math.round(state.fontSize * 100)}%</Label>
          <Slider
            value={[state.fontSize]}
            onValueChange={([value]) => actions.setFontScale(value ?? 1)}
            min={0.75}
            max={2.0}
            step={0.25}
            className='w-full'
          />
        </div>
      </div>
    </div>
  );
}

/**
 * 色盲友好设置组件
 */
function ColorBlindSettings({
  state,
  actions,
  colorBlindTypeOptions,
}: {
  state: { isColorBlindFriendly: boolean; colorBlindType: string };
  actions: {
    setColorBlindMode: (
      type: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia'
    ) => void;
  };
  colorBlindTypeOptions: Array<{ value: string; label: string }>;
}): React.ReactElement {
  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-medium'>色盲友好</h3>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <Label htmlFor='color-blind-friendly'>启用色盲友好模式</Label>
          <Switch
            id='color-blind-friendly'
            checked={state.isColorBlindFriendly}
            onCheckedChange={(checked) =>
              actions.setColorBlindMode(checked ? 'protanopia' : 'none')
            }
          />
        </div>

        <div className='space-y-2'>
          <Label>色盲类型</Label>
          <Select
            value={state.colorBlindType}
            onValueChange={actions.setColorBlindMode}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {colorBlindTypeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

/**
 * 系统信息组件
 */
function SystemInfoSection({
  systemPrefs,
  actions,
}: {
  systemPrefs: {
    hasReducedMotionPreference: boolean;
    hasHighContrastPreference: boolean;
    hasForcedColors: boolean;
    systemFontSize: number;
    colorSchemePreference: string;
  };
  actions: {
    applySystemRecommendations: () => void;
  };
}): React.ReactElement {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='text-sm'>系统偏好检测</CardTitle>
        <CardDescription>当前系统的无障碍偏好设置</CardDescription>
      </CardHeader>
      <CardContent className='space-y-3'>
        <div className='flex items-center justify-between'>
          <span className='text-sm'>减少动画</span>
          <Badge
            variant={
              systemPrefs.hasReducedMotionPreference ? 'default' : 'secondary'
            }
          >
            {systemPrefs.hasReducedMotionPreference ? '已启用' : '未启用'}
          </Badge>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm'>高对比度</span>
          <Badge
            variant={
              systemPrefs.hasHighContrastPreference ? 'default' : 'secondary'
            }
          >
            {systemPrefs.hasHighContrastPreference ? '已启用' : '未启用'}
          </Badge>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm'>强制颜色</span>
          <Badge
            variant={systemPrefs.hasForcedColors ? 'default' : 'secondary'}
          >
            {systemPrefs.hasForcedColors ? '已启用' : '未启用'}
          </Badge>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm'>系统字体大小</span>
          <Badge variant='outline'>
            {Math.round(systemPrefs.systemFontSize * 100)}%
          </Badge>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm'>颜色方案偏好</span>
          <Badge variant='outline'>
            {systemPrefs.colorSchemePreference === 'dark'
              ? '深色'
              : systemPrefs.colorSchemePreference === 'light'
                ? '浅色'
                : '无偏好'}
          </Badge>
        </div>
        <Separator />
        <Button
          variant='outline'
          size='sm'
          onClick={actions.applySystemRecommendations}
          className='w-full'
        >
          应用系统推荐设置
        </Button>
      </CardContent>
    </Card>
  );
}

/**
 * 视觉设置标签页组件
 */
function VisualSettingsTab({
  state,
  actions,
  contrastLevelOptions,
  colorBlindTypeOptions,
}: {
  state: {
    isHighContrast: boolean;
    contrastLevel: string;
    isLargeText: boolean;
    fontSize: number;
    isColorBlindFriendly: boolean;
    colorBlindType: string;
  };
  actions: {
    toggleHighContrast: () => void;
    setContrastLevel: (level: 'normal' | 'high' | 'higher' | 'maximum') => void;
    toggleLargeText: () => void;
    setFontScale: (size: number) => void;
    setColorBlindMode: (
      type: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia'
    ) => void;
  };
  contrastLevelOptions: Array<{ value: string; label: string }>;
  colorBlindTypeOptions: Array<{ value: string; label: string }>;
}): React.ReactElement {
  return (
    <div className='space-y-6'>
      <ContrastSettings
        state={state}
        actions={actions}
        contrastLevelOptions={contrastLevelOptions}
      />
      <FontSettings state={state} actions={actions} />
      <ColorBlindSettings
        state={state}
        actions={actions}
        colorBlindTypeOptions={colorBlindTypeOptions}
      />
    </div>
  );
}

/**
 * 无障碍设置面板组件
 */
export function AccessibilitySettingsPanel({
  className = '',
  showSystemInfo = true,
  showPreview: _showPreview = false,
  onConfigChange,
}: AccessibilitySettingsPanelProps): React.ReactElement {
  const [state, actions] = useAccessibilityTheme(
    onConfigChange !== undefined ? { onConfigChange } : {}
  );
  const systemPrefs = useSystemAccessibilityPreferences();
  const [activeTab, setActiveTab] = useState('visual');

  /**
   * 对比度级别选项
   */
  const contrastLevelOptions = [
    { value: 'normal', label: '标准对比度' },
    { value: 'high', label: '高对比度' },
    { value: 'higher', label: '更高对比度' },
    { value: 'maximum', label: '最高对比度' },
  ];

  /**
   * 色盲类型选项
   */
  const colorBlindTypeOptions = [
    { value: 'none', label: '无色盲' },
    { value: 'protanopia', label: '红色盲' },
    { value: 'deuteranopia', label: '绿色盲' },
    { value: 'tritanopia', label: '蓝色盲' },
    { value: 'achromatopsia', label: '全色盲' },
  ];

  /**
   * 渲染系统信息
   */
  const renderSystemInfo = (): React.ReactElement => (
    <SystemInfoSection systemPrefs={systemPrefs} actions={actions} />
  );

  /**
   * 渲染视觉设置
   */
  const renderVisualSettings = (): React.ReactElement => (
    <VisualSettingsTab
      state={state}
      actions={actions}
      contrastLevelOptions={contrastLevelOptions}
      colorBlindTypeOptions={colorBlindTypeOptions}
    />
  );

  /**
   * 渲染动画设置
   */
  const renderMotionSettings = (): React.ReactElement => (
    <MotionSettingsTab state={state} actions={actions} />
  );

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>无障碍设置</CardTitle>
          <CardDescription>配置无障碍功能以改善使用体验</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='visual'>视觉设置</TabsTrigger>
              <TabsTrigger value='motion'>动画设置</TabsTrigger>
            </TabsList>

            <TabsContent value='visual' className='mt-6'>
              {renderVisualSettings()}
            </TabsContent>

            <TabsContent value='motion' className='mt-6'>
              {renderMotionSettings()}
            </TabsContent>
          </Tabs>

          <Separator className='my-6' />

          <div className='flex gap-2'>
            <Button
              variant='outline'
              onClick={actions.resetToDefaults}
              size='sm'
            >
              重置默认
            </Button>
            <Button
              variant='outline'
              onClick={actions.applySystemRecommendations}
              size='sm'
            >
              应用系统推荐
            </Button>
          </div>
        </CardContent>
      </Card>

      {showSystemInfo && renderSystemInfo()}
    </div>
  );
}

/**
 * 动画设置标签页组件
 */
function MotionSettingsTab({
  state,
  actions,
}: {
  state: {
    isReducedMotion: boolean;
    config: { animationDuration: number };
  };
  actions: {
    toggleReducedMotion: () => void;
    updateConfig: (updates: { animationDuration: number }) => void;
  };
}): React.ReactElement {
  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='text-sm'>动画设置</CardTitle>
          <CardDescription>控制界面动画和过渡效果</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <Label htmlFor='reduced-motion'>减少动画</Label>
            <Switch
              id='reduced-motion'
              checked={state.isReducedMotion}
              onCheckedChange={actions.toggleReducedMotion}
            />
          </div>

          <div className='space-y-2'>
            <Label>
              动画速度: {Math.round(state.config.animationDuration * 100)}%
            </Label>
            <Slider
              value={[state.config.animationDuration]}
              onValueChange={(value) =>
                actions.updateConfig({ animationDuration: value[0] ?? 1 })
              }
              min={0}
              max={2.0}
              step={0.25}
              className='w-full'
              disabled={state.isReducedMotion}
            />
            <div className='text-muted-foreground flex justify-between text-xs'>
              <span>禁用</span>
              <span>标准</span>
              <span>慢速</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
