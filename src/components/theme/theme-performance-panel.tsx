/**
 * 主题性能监控面板组件
 *
 * 功能特性：
 * - 性能指标显示
 * - 缓存管理
 * - 性能优化控制
 * - 实时监控
 * - 性能报告
 */
'use client';

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useCacheManagement,
  usePerformanceMetrics,
  useThemePerformance,
} from '@/hooks/use-theme-performance';

/**
 * 主题性能监控面板组件
 *
 * 功能特性：
 * - 性能指标显示
 * - 缓存管理
 * - 性能优化控制
 * - 实时监控
 * - 性能报告
 */

/**
 * 主题性能监控面板组件
 *
 * 功能特性：
 * - 性能指标显示
 * - 缓存管理
 * - 性能优化控制
 * - 实时监控
 * - 性能报告
 */

// 组件属性
export interface ThemePerformancePanelProps {
  className?: string;
  showAdvanced?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onConfigChange?: (config: any) => void;
}

/**
 * 主题性能监控面板组件
 */
export function ThemePerformancePanel({
  className = '',
  showAdvanced = false,
  autoRefresh = true,
  refreshInterval = 5000,
  onConfigChange,
}: ThemePerformancePanelProps) {
  const [state, actions] = useThemePerformance({
    autoOptimize: false,
    monitoringInterval: autoRefresh ? refreshInterval : 0,
    onPerformanceUpdate: (metrics) => {
      if (onConfigChange) {
        onConfigChange({ metrics });
      }
    },
  });

  const metrics = usePerformanceMetrics(refreshInterval);
  const { cacheInfo, clearCache, warmupCache } = useCacheManagement();
  const [activeTab, setActiveTab] = useState('overview');
  const [isOptimizing, setIsOptimizing] = useState(false);

  // 格式化数字
  const formatNumber = (num: number, decimals = 2): string => {
    return num.toFixed(decimals);
  };

  // 格式化字节大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
  };

  // 获取性能状态颜色
  const getPerformanceColor = (
    value: number,
    thresholds: { good: number; warning: number }
  ) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  // 优化性能
  const handleOptimize = async () => {
    setIsOptimizing(true);
    try {
      actions.optimizePerformance();
      await new Promise((resolve) => setTimeout(resolve, 1000)); // 模拟优化过程
    } finally {
      setIsOptimizing(false);
    }
  };

  // 预热缓存
  const handleWarmupCache = async () => {
    const commonThemes = ['light', 'dark', 'system'];
    await warmupCache(commonThemes);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle>主题性能监控</CardTitle>
          <CardDescription>实时监控主题加载性能和缓存状态</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-4'>
              <TabsTrigger value='overview'>概览</TabsTrigger>
              <TabsTrigger value='cache'>缓存</TabsTrigger>
              <TabsTrigger value='config'>配置</TabsTrigger>
              <TabsTrigger value='report'>报告</TabsTrigger>
            </TabsList>

            {/* 概览标签页 */}
            <TabsContent value='overview' className='space-y-4'>
              <div className='grid grid-cols-2 gap-4 md:grid-cols-4'>
                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>
                      {formatNumber(metrics.loadTime)}ms
                    </div>
                    <p className='text-muted-foreground text-xs'>加载时间</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>
                      {formatNumber(metrics.cacheHitRate)}%
                    </div>
                    <p className='text-muted-foreground text-xs'>缓存命中率</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>
                      {formatNumber(metrics.memoryUsage)}MB
                    </div>
                    <p className='text-muted-foreground text-xs'>内存使用</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>
                      {metrics.totalThemesLoaded}
                    </div>
                    <p className='text-muted-foreground text-xs'>已加载主题</p>
                  </CardContent>
                </Card>
              </div>

              <div className='space-y-4'>
                <div>
                  <div className='mb-2 flex items-center justify-between'>
                    <Label>加载状态</Label>
                    <Badge
                      variant={
                        state.loadingState.isLoading ? 'default' : 'secondary'
                      }
                    >
                      {state.loadingState.isLoading ? '加载中' : '空闲'}
                    </Badge>
                  </div>
                  <Progress
                    value={state.loadingState.loadingProgress}
                    className='w-full'
                  />
                </div>

                <div className='flex gap-2'>
                  <Button
                    onClick={handleOptimize}
                    disabled={isOptimizing}
                    variant='default'
                  >
                    {isOptimizing ? '优化中...' : '优化性能'}
                  </Button>
                  <Button onClick={actions.resetMetrics} variant='outline'>
                    重置指标
                  </Button>
                  <Button onClick={handleWarmupCache} variant='outline'>
                    预热缓存
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* 缓存标签页 */}
            <TabsContent value='cache' className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>
                      {formatBytes(cacheInfo.size)}
                    </div>
                    <p className='text-muted-foreground text-xs'>缓存大小</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>{cacheInfo.items}</div>
                    <p className='text-muted-foreground text-xs'>缓存项目</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className='p-4'>
                    <div className='text-2xl font-bold'>
                      {formatNumber(cacheInfo.hitRate)}%
                    </div>
                    <p className='text-muted-foreground text-xs'>命中率</p>
                  </CardContent>
                </Card>
              </div>

              <div className='space-y-4'>
                <div>
                  <Label>已加载主题</Label>
                  <div className='mt-2 flex flex-wrap gap-2'>
                    {Array.from(state.loadingState.loadedThemes).map(
                      (theme) => (
                        <Badge key={theme} variant='secondary'>
                          {theme}
                        </Badge>
                      )
                    )}
                  </div>
                </div>

                <div>
                  <Label>预加载主题</Label>
                  <div className='mt-2 flex flex-wrap gap-2'>
                    {Array.from(state.loadingState.preloadedThemes).map(
                      (theme) => (
                        <Badge key={theme} variant='default'>
                          {theme}
                        </Badge>
                      )
                    )}
                  </div>
                </div>

                <Separator />

                <div className='flex gap-2'>
                  <Button onClick={clearCache} variant='destructive'>
                    清除缓存
                  </Button>
                  <Button onClick={handleWarmupCache} variant='outline'>
                    预热常用主题
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* 配置标签页 */}
            <TabsContent value='config' className='space-y-4'>
              <div className='space-y-6'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>启用预加载</Label>
                    <p className='text-muted-foreground text-sm'>
                      自动预加载常用主题
                    </p>
                  </div>
                  <Switch
                    checked={state.config.enablePreloading}
                    onCheckedChange={(checked) =>
                      actions.updateConfig({ enablePreloading: checked })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>启用缓存</Label>
                    <p className='text-muted-foreground text-sm'>
                      缓存已加载的主题数据
                    </p>
                  </div>
                  <Switch
                    checked={state.config.enableCaching}
                    onCheckedChange={(checked) =>
                      actions.updateConfig({ enableCaching: checked })
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>启用压缩</Label>
                    <p className='text-muted-foreground text-sm'>
                      压缩缓存数据以节省内存
                    </p>
                  </div>
                  <Switch
                    checked={state.config.compressionEnabled}
                    onCheckedChange={(checked) =>
                      actions.updateConfig({ compressionEnabled: checked })
                    }
                  />
                </div>

                <div className='space-y-2'>
                  <Label>缓存大小限制 (MB)</Label>
                  <Slider
                    value={[state.config.cacheSize]}
                    onValueChange={([value]) =>
                      actions.updateConfig({ cacheSize: value ?? 50 })
                    }
                    max={200}
                    min={10}
                    step={10}
                    className='w-full'
                  />
                  <p className='text-muted-foreground text-sm'>
                    当前: {state.config.cacheSize}MB
                  </p>
                </div>

                {showAdvanced && (
                  <>
                    <Separator />
                    <div className='space-y-4'>
                      <h4 className='text-sm font-medium'>高级设置</h4>

                      <div className='space-y-2'>
                        <Label>内存阈值 (MB)</Label>
                        <Slider
                          value={[state.config.memoryThreshold]}
                          onValueChange={([value]) =>
                            actions.updateConfig({
                              memoryThreshold: value ?? 100,
                            })
                          }
                          max={500}
                          min={50}
                          step={25}
                          className='w-full'
                        />
                        <p className='text-muted-foreground text-sm'>
                          当前: {state.config.memoryThreshold}MB
                        </p>
                      </div>

                      <div className='flex items-center justify-between'>
                        <div className='space-y-0.5'>
                          <Label>性能监控</Label>
                          <p className='text-muted-foreground text-sm'>
                            启用详细的性能监控
                          </p>
                        </div>
                        <Switch
                          checked={state.config.performanceMonitoring}
                          onCheckedChange={(checked) =>
                            actions.updateConfig({
                              performanceMonitoring: checked,
                            })
                          }
                        />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>

            {/* 报告标签页 */}
            <TabsContent value='report' className='space-y-4'>
              <div className='space-y-4'>
                <div>
                  <h4 className='mb-2 text-sm font-medium'>性能摘要</h4>
                  <div className='grid grid-cols-2 gap-4 text-sm'>
                    <div>
                      <span className='text-muted-foreground'>
                        平均加载时间:
                      </span>
                      <span
                        className={`ml-2 font-medium ${getPerformanceColor(metrics.averageLoadTime, { good: 500, warning: 1000 })}`}
                      >
                        {formatNumber(metrics.averageLoadTime)}ms
                      </span>
                    </div>
                    <div>
                      <span className='text-muted-foreground'>网络请求:</span>
                      <span className='ml-2 font-medium'>
                        {metrics.networkRequests}
                      </span>
                    </div>
                    <div>
                      <span className='text-muted-foreground'>压缩比率:</span>
                      <span className='ml-2 font-medium'>
                        {formatNumber(metrics.compressionRatio * 100)}%
                      </span>
                    </div>
                    <div>
                      <span className='text-muted-foreground'>错误率:</span>
                      <span
                        className={`ml-2 font-medium ${getPerformanceColor(metrics.errorRate, { good: 1, warning: 5 })}`}
                      >
                        {formatNumber(metrics.errorRate)}%
                      </span>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className='mb-2 text-sm font-medium'>优化建议</h4>
                  <div className='space-y-2'>
                    {(() => {
                      const report = actions.getPerformanceReport();
                      return report.recommendations.map(
                        (recommendation: string, index: number) => (
                          <div
                            key={index}
                            className='text-muted-foreground text-sm'
                          >
                            • {recommendation}
                          </div>
                        )
                      );
                    })()}
                  </div>
                </div>

                <Button
                  onClick={() => {
                    const report = actions.getPerformanceReport();
                    console.log('Performance Report:', report);
                  }}
                  variant='outline'
                  className='w-full'
                >
                  导出详细报告
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
