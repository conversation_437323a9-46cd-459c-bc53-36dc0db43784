/**
 * Tailwind CSS 动画配置和 Intersection Observer 工具
 * 采用渐进式动画策略：优先使用 Tailwind CSS 动画（零包体积）
 */

// 动画类型
export type AnimationType =
  | 'fadeIn'
  | 'slideUp'
  | 'slideDown'
  | 'slideLeft'
  | 'slideRight'
  | 'scaleUp'
  | 'scaleDown'
  | 'rotateIn'
  | 'bounceIn'
  | 'flipIn';

// 动画配置类型
export interface AnimationConfig {
  duration: string;
  delay?: string;
  easing?: string;
  threshold?: number;
  rootMargin?: string;
}

// 常量定义
const DEFAULT_DURATION = 'duration-700';
const FAST_DURATION = 'duration-500';
const SLOW_DURATION = 'duration-1000';
const DEFAULT_EASING = 'ease-out';
const EASE_IN_OUT = 'ease-in-out';
const DEFAULT_THRESHOLD = 0.1;
const MEDIUM_THRESHOLD = 0.2;
const HIGH_THRESHOLD = 0.3;
const VERY_HIGH_THRESHOLD = 0.5;
const DEFAULT_ROOT_MARGIN = '0px 0px -50px 0px';

// 预定义的动画配置
export const animationConfigs = {
  // 淡入动画
  fadeIn: {
    duration: DEFAULT_DURATION,
    easing: DEFAULT_EASING,
    threshold: DEFAULT_THRESHOLD,
    rootMargin: DEFAULT_ROOT_MARGIN,
  },
  // 从下方滑入
  slideUp: {
    duration: DEFAULT_DURATION,
    easing: DEFAULT_EASING,
    threshold: DEFAULT_THRESHOLD,
    rootMargin: DEFAULT_ROOT_MARGIN,
  },
  // 从上方滑入
  slideDown: {
    duration: DEFAULT_DURATION,
    easing: DEFAULT_EASING,
    threshold: DEFAULT_THRESHOLD,
    rootMargin: DEFAULT_ROOT_MARGIN,
  },
  // 从左侧滑入
  slideLeft: {
    duration: FAST_DURATION,
    easing: DEFAULT_EASING,
    threshold: MEDIUM_THRESHOLD,
  },
  // 从右侧滑入
  slideRight: {
    duration: FAST_DURATION,
    easing: DEFAULT_EASING,
    threshold: MEDIUM_THRESHOLD,
  },
  // 放大动画
  scaleUp: {
    duration: FAST_DURATION,
    easing: DEFAULT_EASING,
    threshold: HIGH_THRESHOLD,
  },
  // 缩小动画
  scaleDown: {
    duration: FAST_DURATION,
    easing: DEFAULT_EASING,
    threshold: HIGH_THRESHOLD,
  },
  // 旋转进入动画
  rotateIn: {
    duration: SLOW_DURATION,
    easing: EASE_IN_OUT,
    threshold: VERY_HIGH_THRESHOLD,
  },
  // 弹跳进入动画
  bounceIn: {
    duration: SLOW_DURATION,
    easing: DEFAULT_EASING,
    threshold: HIGH_THRESHOLD,
  },
  // 翻转进入动画
  flipIn: {
    duration: DEFAULT_DURATION,
    easing: EASE_IN_OUT,
    threshold: 0.4,
  },
} as const;

// 动画类名常量
const OPACITY_HIDDEN = 'opacity-0';
const OPACITY_VISIBLE = 'opacity-100';
const TRANSFORM_NONE = 'translate-y-0';
const SCALE_NORMAL = 'scale-100';
const ROTATE_NONE = 'rotate-0';
const TRANSITION_ALL = 'transition-all';

// 动画类名映射
export const animationClasses = {
  // 初始状态（隐藏）
  initial: {
    fadeIn: OPACITY_HIDDEN,
    slideUp: `${OPACITY_HIDDEN} translate-y-8`,
    slideDown: `${OPACITY_HIDDEN} -translate-y-8`,
    slideLeft: `${OPACITY_HIDDEN} -translate-x-8`,
    slideRight: `${OPACITY_HIDDEN} translate-x-8`,
    scaleUp: `${OPACITY_HIDDEN} scale-95`,
    scaleDown: `${OPACITY_HIDDEN} scale-105`,
    rotateIn: `${OPACITY_HIDDEN} rotate-12`,
    bounceIn: `${OPACITY_HIDDEN} scale-95`,
    flipIn: `${OPACITY_HIDDEN} rotate-y-90`,
  },
  // 动画状态（显示）
  animate: {
    fadeIn: OPACITY_VISIBLE,
    slideUp: `${OPACITY_VISIBLE} ${TRANSFORM_NONE}`,
    slideDown: `${OPACITY_VISIBLE} ${TRANSFORM_NONE}`,
    slideLeft: `${OPACITY_VISIBLE} translate-x-0`,
    slideRight: `${OPACITY_VISIBLE} translate-x-0`,
    scaleUp: `${OPACITY_VISIBLE} ${SCALE_NORMAL}`,
    scaleDown: `${OPACITY_VISIBLE} ${SCALE_NORMAL}`,
    rotateIn: `${OPACITY_VISIBLE} ${ROTATE_NONE}`,
    bounceIn: `${OPACITY_VISIBLE} ${SCALE_NORMAL}`,
    flipIn: `${OPACITY_VISIBLE} rotate-y-0`,
  },
  // 过渡类名
  transition: {
    fadeIn: 'transition-opacity',
    slideUp: TRANSITION_ALL,
    slideDown: TRANSITION_ALL,
    slideLeft: TRANSITION_ALL,
    slideRight: TRANSITION_ALL,
    scaleUp: TRANSITION_ALL,
    scaleDown: TRANSITION_ALL,
    rotateIn: TRANSITION_ALL,
    bounceIn: TRANSITION_ALL,
    flipIn: TRANSITION_ALL,
  },
} as const;

// Intersection Observer 工具类
export class ScrollAnimationObserver {
  private observer: IntersectionObserver | null = null;
  private elements: Map<
    Element,
    {
      config: AnimationConfig;
      callback?: (element: Element, animationType: AnimationType) => void;
      animationType?: AnimationType;
    }
  > = new Map();

  constructor() {
    if (
      typeof window !== 'undefined' &&
      typeof IntersectionObserver !== 'undefined'
    ) {
      this.initObserver();
    }
  }

  private initObserver(): void {
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const elementData = this.elements.get(entry.target);
          if (!elementData) return;

          if (entry.isIntersecting) {
            this.animateIn(entry.target);
            // 调用回调函数（如果提供）
            if (elementData.callback && elementData.animationType) {
              elementData.callback(entry.target, elementData.animationType);
            }
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
      }
    );
  }

  private animateIn(element: Element): void {
    const animationType = element.getAttribute(
      'data-animation'
    ) as keyof typeof animationClasses.initial;
    if (!animationType) return;

    // 移除初始状态类名
    const initialClasses = animationClasses.initial[animationType].split(' ');
    element.classList.remove(...initialClasses);

    // 添加动画状态类名
    const animateClasses = animationClasses.animate[animationType].split(' ');
    element.classList.add(...animateClasses);

    // 添加过渡类名
    const transitionClasses =
      animationClasses.transition[animationType].split(' ');
    element.classList.add(...transitionClasses);

    // 添加持续时间
    const config = animationConfigs[animationType];
    if (config?.duration) {
      element.classList.add(config.duration);
    }
  }

  // 重载方法签名
  public observe(element: Element, config?: AnimationConfig): void;
  public observe(
    element: Element,
    animationType: AnimationType,
    callback?: (element: Element, animationType: AnimationType) => void
  ): void;
  public observe(
    element: Element,
    configOrAnimationType?: AnimationConfig | AnimationType,
    callback?: (element: Element, animationType: AnimationType) => void
  ): void {
    let finalConfig: AnimationConfig;
    let animationType: AnimationType | undefined;

    if (typeof configOrAnimationType === 'string') {
      // 第二种签名：animationType + callback
      animationType = configOrAnimationType;
      finalConfig = animationConfigs[animationType];
    } else {
      // 第一种签名：config
      finalConfig = configOrAnimationType || animationConfigs.fadeIn;
    }

    const elementData: {
      config: AnimationConfig;
      callback?: (element: Element, animationType: AnimationType) => void;
      animationType?: AnimationType;
    } = {
      config: finalConfig,
    };

    if (callback !== undefined) {
      elementData.callback = callback;
    }
    if (animationType !== undefined) {
      elementData.animationType = animationType;
    }

    this.elements.set(element, elementData);

    // 如果没有 observer，尝试初始化
    if (!this.observer) {
      if (
        typeof window !== 'undefined' &&
        typeof IntersectionObserver !== 'undefined'
      ) {
        this.initObserver();
      } else {
        return; // 在测试环境或不支持的环境中直接返回
      }
    }

    // 更新 observer 配置
    if (finalConfig?.threshold !== undefined || finalConfig?.rootMargin) {
      if (this.observer && typeof this.observer.disconnect === 'function') {
        this.observer.disconnect();
      }
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            const elementData = this.elements.get(entry.target);
            if (!elementData) return;

            if (entry.isIntersecting) {
              this.animateIn(entry.target);
              // 调用回调函数（如果提供）
              if (elementData.callback && elementData.animationType) {
                elementData.callback(entry.target, elementData.animationType);
              }
            }
          });
        },
        {
          threshold: finalConfig?.threshold || 0.1,
          rootMargin: finalConfig?.rootMargin || '0px 0px -50px 0px',
        }
      );

      // 重新观察所有元素
      this.elements.forEach((_, el) => {
        if (this.observer && typeof this.observer.observe === 'function') {
          this.observer.observe(el);
        }
      });
    } else {
      if (this.observer && typeof this.observer.observe === 'function') {
        this.observer.observe(element);
      }
    }
  }

  public unobserve(element: Element): void {
    if (this.observer && typeof this.observer.unobserve === 'function') {
      this.observer.unobserve(element);
    }
    this.elements.delete(element);
  }

  public disconnect(): void {
    if (this.observer && typeof this.observer.disconnect === 'function') {
      this.observer.disconnect();
    }
    this.elements.clear();
    this.observer = null;
  }
}

// 全局动画观察器实例
export const scrollAnimationObserver = new ScrollAnimationObserver();

// 工具函数：获取动画类名
export function getAnimationClasses(
  type: keyof typeof animationClasses.initial,
  isVisible: boolean = false
): string {
  const initial = animationClasses.initial[type];
  const animate = animationClasses.animate[type];
  const transition = animationClasses.transition[type];
  const duration = animationConfigs[type]?.duration || 'duration-500';

  return `${transition} ${duration} ${isVisible ? animate : initial}`;
}

// 工具函数：延迟动画
export function delayedAnimation(delay: number): string {
  const delayClass = `delay-${delay}`;
  return delayClass;
}

// 预设动画组合
export const presetAnimations = {
  // 卡片悬停效果
  cardHover:
    'transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg',
  // 按钮悬停效果
  buttonHover:
    'transition-all duration-200 ease-out hover:scale-105 active:scale-95',
  // 链接悬停效果
  linkHover: 'transition-colors duration-200 ease-out hover:text-primary',
  // 图标旋转
  iconSpin: 'transition-transform duration-300 ease-out hover:rotate-12',
  // 导航项悬停
  navHover:
    'transition-all duration-200 ease-out hover:bg-accent hover:text-accent-foreground',
  // 主题切换动画
  themeTransition: 'transition-colors duration-300 ease-out',
} as const;

// 响应式动画工具
export const responsiveAnimations = {
  // 移动端减少动画
  mobile: 'motion-reduce:transition-none motion-reduce:transform-none',
  // 桌面端增强动画
  desktop: 'motion-safe:transition-all motion-safe:duration-300',
} as const;
