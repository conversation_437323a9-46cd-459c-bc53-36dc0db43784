import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getTranslations } from 'next-intl/server';
import { DemoSection } from '@/components/content/demo-section';
import { FeaturesSection } from '@/components/content/features-section';
import { HeroSection } from '@/components/content/hero-section';
import { TechStackSection } from '@/components/content/tech-stack-section';

/**
 * 主页 - 技术展示页面
 *
 * 特性：
 * - Hero 区域：项目介绍和 CTA
 * - 技术栈展示：完整的技术组件展示
 * - 组件演示：shadcn/ui 组件库展示
 * - 特性展示：核心功能特性
 * - 响应式设计：适配所有设备
 * - 动画效果：流畅的滚动动画
 * - SEO 优化：完整的元数据配置
 */

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'common' });
  const homeT = await getTranslations({ locale, namespace: 'home.hero' });

  return {
    title: `${t('title')} - ${t('description')}`,
    description: homeT('description'),
    keywords: [
      'Next.js',
      'TypeScript',
      'Tailwind CSS',
      'shadcn/ui',
      'React',
      'B2B',
      'Enterprise',
      'Website Template',
      'Modern Web Development',
    ],
    authors: [{ name: 'Tucsenberg' }],
    creator: 'Tucsenberg',
    publisher: 'Tucsenberg',
    openGraph: {
      title: `${t('title')} - ${t('description')}`,
      description: homeT('description'),
      type: 'website',
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      siteName: t('title'),
    },
    twitter: {
      card: 'summary_large_image',
      title: `${t('title')} - ${t('description')}`,
      description: homeT('description'),
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
    },
  };
}

export default function Home({ params: _params }: PageProps): ReactElement {
  return (
    <div className='bg-background min-h-screen'>
      {/* Hero 区域 */}
      <HeroSection />

      {/* 技术栈展示 */}
      <TechStackSection />

      {/* 特性展示 */}
      <FeaturesSection />

      {/* 组件演示 */}
      <DemoSection />
    </div>
  );
}
