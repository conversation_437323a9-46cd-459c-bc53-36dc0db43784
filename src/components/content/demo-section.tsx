'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { ButtonDemo } from '@/components/demo/button-demo';
import { FeatureCards } from '@/components/demo/feature-cards';
import { InputDemo } from '@/components/demo/input-demo';
import { InteractionDemo } from '@/components/demo/interaction-demo';
import { LanguageSwitcher } from '@/components/shared/language-switcher';
import { ThemeSwitcher } from '@/components/shared/theme-switcher';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

/**
 * 演示区域组件
 */
export function DemoSection(): React.JSX.Element {
  const t = useTranslations('home.demo');

  return (
    <section id='demo-section' className='bg-muted/30 py-20 md:py-32'>
      <div className='container mx-auto px-4'>
        <motion.div
          className='mx-auto max-w-4xl text-center'
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-foreground mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl'>
            {t('title')}
          </h2>
          <p className='text-muted-foreground mb-16 text-lg'>{t('subtitle')}</p>
        </motion.div>

        <div className='mx-auto max-w-6xl space-y-8'>
          <Card>
            <CardHeader>
              <CardTitle>{t('sections.ui.title')}</CardTitle>
              <CardDescription>{t('sections.ui.description')}</CardDescription>
            </CardHeader>
            <CardContent className='space-y-8'>
              <div className='grid gap-8 md:grid-cols-2'>
                <div>
                  <h4 className='mb-4 text-lg font-semibold'>
                    {t('sections.ui.buttonComponents')}
                  </h4>
                  <ButtonDemo />
                </div>
                <div>
                  <h4 className='mb-4 text-lg font-semibold'>
                    {t('sections.ui.inputComponents')}
                  </h4>
                  <InputDemo />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('sections.features.title')}</CardTitle>
              <CardDescription>
                {t('sections.features.description')}
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-8'>
              <div>
                <h4 className='mb-4 text-lg font-semibold'>
                  {t('sections.features.featureCards')}
                </h4>
                <FeatureCards />
              </div>
              <div>
                <h4 className='mb-4 text-lg font-semibold'>
                  {t('sections.features.interactiveElements')}
                </h4>
                <InteractionDemo />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('sections.config.title')}</CardTitle>
              <CardDescription>
                {t('sections.config.description')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex items-center justify-center gap-8'>
                <div className='flex items-center gap-2'>
                  <span className='text-sm font-medium'>Theme:</span>
                  <ThemeSwitcher />
                </div>
                <div className='flex items-center gap-2'>
                  <span className='text-sm font-medium'>Language:</span>
                  <LanguageSwitcher />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
