import { type NextRequest, NextResponse } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

/**
 * 智能语言检测中间件
 *
 * 功能特性：
 * - 集成 next-intl 4.3.4 中间件
 * - 服务端智能语言检测
 * - Accept-Language 头部解析
 * - 地理位置推断（基于时区）
 * - 智能重定向和语言协商
 */

// 支持的语言类型
type SupportedLocale = (typeof routing.locales)[number];

// 浏览器语言到支持语言的映射
const BROWSER_LOCALE_MAP: Record<string, SupportedLocale> = {
  // 中文变体
  zh: 'zh',
  'zh-CN': 'zh',
  'zh-Hans': 'zh',
  'zh-Hans-CN': 'zh',
  'zh-TW': 'zh',
  'zh-Hant': 'zh',
  'zh-Hant-TW': 'zh',
  'zh-HK': 'zh',
  'zh-MO': 'zh',
  'zh-SG': 'zh',

  // 英文变体
  en: 'en',
  'en-US': 'en',
  'en-GB': 'en',
  'en-CA': 'en',
  'en-AU': 'en',
  'en-NZ': 'en',
  'en-IE': 'en',
  'en-ZA': 'en',
} as const;

/**
 * 解析 Accept-Language 头部
 */
function parseAcceptLanguageHeader(
  acceptLanguage: string
): Array<{ locale: string; quality: number }> {
  return acceptLanguage
    .split(',')
    .map((lang) => {
      const [locale, q = '1'] = lang.trim().split(';q=');
      return {
        locale: locale?.trim() ?? '',
        quality: Number.parseFloat(q),
      };
    })
    .filter((item) => item.locale.length > 0)
    .sort((a, b) => b.quality - a.quality);
}

/**
 * 查找匹配的语言
 */
// 辅助函数：检查直接匹配
function checkDirectMatch(
  locale: string,
  localeMap: Map<string, SupportedLocale>
): SupportedLocale | null {
  if (localeMap.has(locale)) {
    const result = localeMap.get(locale);
    return result ?? null;
  }
  return null;
}

// 辅助函数：检查语言代码匹配
function checkLanguageCodeMatch(
  locale: string,
  localeMap: Map<string, SupportedLocale>
): SupportedLocale | null {
  const langCode = locale.split('-')[0];
  if (
    langCode !== undefined &&
    langCode.length > 0 &&
    localeMap.has(langCode)
  ) {
    const result = localeMap.get(langCode);
    return result ?? null;
  }
  return null;
}

function findMatchingLocale(
  languages: Array<{ locale: string; quality: number }>
): SupportedLocale | null {
  const localeMap = new Map(Object.entries(BROWSER_LOCALE_MAP));

  for (const { locale } of languages) {
    // 直接匹配
    const directMatch = checkDirectMatch(locale, localeMap);
    if (directMatch !== null) return directMatch;

    // 语言代码匹配（忽略地区）
    const langCodeMatch = checkLanguageCodeMatch(locale, localeMap);
    if (langCodeMatch !== null) return langCodeMatch;
  }

  return null;
}

/**
 * 从 Accept-Language 头部检测语言偏好
 */
function detectLocaleFromAcceptLanguage(
  acceptLanguage: string | null
): SupportedLocale | null {
  if (acceptLanguage === null || acceptLanguage.length === 0) return null;

  const languages = parseAcceptLanguageHeader(acceptLanguage);
  return findMatchingLocale(languages);
}

/**
 * 从时区头部推断地理位置
 */
function detectLocaleFromTimezone(
  request: NextRequest
): SupportedLocale | null {
  // 尝试从自定义头部获取时区信息
  const timezone = request.headers.get('x-timezone');

  if (timezone !== null && timezone.length > 0) {
    // 中国时区
    if (
      timezone.includes('Asia/Shanghai') ||
      timezone.includes('Asia/Hong_Kong') ||
      timezone.includes('Asia/Taipei') ||
      timezone.includes('Asia/Macau')
    ) {
      return 'zh';
    }
  }

  return null;
}

/**
 * 智能语言检测中间件
 */
function smartLanguageMiddleware(
  request: NextRequest
): NextResponse | undefined {
  const pathname = request.nextUrl.pathname;

  // 如果路径已经包含语言代码，直接使用 next-intl 中间件
  const pathSegments = pathname.split('/').filter(Boolean);
  const firstSegment = pathSegments.find(Boolean);

  if (
    firstSegment !== undefined &&
    firstSegment.length > 0 &&
    routing.locales.includes(firstSegment as SupportedLocale)
  ) {
    return createMiddleware(routing)(request);
  }

  // 对于根路径，执行智能检测
  if (pathname === '/') {
    let detectedLocale: SupportedLocale | null = null;

    // 1. 检查 Accept-Language 头部
    const acceptLanguage = request.headers.get('accept-language');
    detectedLocale = detectLocaleFromAcceptLanguage(acceptLanguage);

    // 2. 如果没有检测到，尝试时区推断
    detectedLocale ??= detectLocaleFromTimezone(request);

    // 3. 使用检测到的语言或默认语言
    const targetLocale = detectedLocale ?? routing.defaultLocale;

    // 重定向到检测到的语言路径
    const url = request.nextUrl.clone();
    url.pathname = `/${targetLocale}${pathname === '/' ? '' : pathname}`;

    // 添加检测信息到响应头（用于调试）
    const response = NextResponse.redirect(url);
    response.headers.set('x-detected-locale', targetLocale);
    response.headers.set(
      'x-detection-source',
      detectedLocale !== null ? 'smart' : 'default'
    );

    return response;
  }

  // 对于其他路径，使用默认的 next-intl 中间件
  return createMiddleware(routing)(request);
}

export default smartLanguageMiddleware;

export const config = {
  // 匹配所有路径，除了静态资源和 API 路由
  matcher: [
    // 匹配所有路径
    '/((?!api|_next|_vercel|.*\\..*).*)',
    // 匹配根路径
    '/',
    // 匹配国际化路径
    '/(en|zh)/:path*',
  ],
};
