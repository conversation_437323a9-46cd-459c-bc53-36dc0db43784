/**
 * 智能主题控制器组件
 *
 * 功能特性：
 * - 智能主题适配建议显示
 * - 用户交互和反馈收集
 * - 适配历史和统计展示
 * - 配置和设置管理
 * - 实时状态监控
 * - 调试信息展示
 */
'use client';

import React, { useState } from 'react';
import {
  Activity,
  BarChart3,
  CheckCircle,
  Clock,
  Eye,
  Info,
  Lightbulb,
  Monitor,
  Moon,
  Settings,
  Sun,
  TrendingUp,
  XCircle,
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useSmartThemeAdaptation } from '@/hooks/use-smart-theme-adaptation';
import type {
  SupportedTheme,
  ThemeAdaptationSuggestion,
  UserActivity,
} from '@/lib/theme/smart-theme-adapter';

/**
 * 智能主题控制器组件
 *
 * 功能特性：
 * - 智能主题适配建议显示
 * - 用户交互和反馈收集
 * - 适配历史和统计展示
 * - 配置和设置管理
 * - 实时状态监控
 * - 调试信息展示
 */

/**
 * 智能主题控制器组件
 *
 * 功能特性：
 * - 智能主题适配建议显示
 * - 用户交互和反馈收集
 * - 适配历史和统计展示
 * - 配置和设置管理
 * - 实时状态监控
 * - 调试信息展示
 */

// 主题图标映射
const themeIcons: Record<SupportedTheme, React.ReactNode> = {
  light: <Sun className='h-4 w-4' />,
  dark: <Moon className='h-4 w-4' />,
  auto: <Monitor className='h-4 w-4' />,
  system: <Monitor className='h-4 w-4' />,
};

// 适配原因图标映射
const reasonIcons: Record<string, React.ReactNode> = {
  'time-based': <Clock className='h-4 w-4' />,
  'ambient-light': <Eye className='h-4 w-4' />,
  'user-preference': <Settings className='h-4 w-4' />,
  'activity-based': <Activity className='h-4 w-4' />,
  accessibility: <Info className='h-4 w-4' />,
};

// 组件属性
export interface SmartThemeControllerProps {
  className?: string;
  showDebugInfo?: boolean;
  enableNotifications?: boolean;
  autoAdaptationEnabled?: boolean;
}

/**
 * 智能主题控制器组件
 */
export function SmartThemeController({
  className,
  showDebugInfo = false,
  enableNotifications = false,
  autoAdaptationEnabled = true,
}: SmartThemeControllerProps) {
  const [state, actions] = useSmartThemeAdaptation({
    enableAutoAdaptation: autoAdaptationEnabled,
    enableNotifications,
    debugMode: showDebugInfo,
  });

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [confidenceThreshold, setConfidenceThreshold] = useState(70);

  /**
   * 渲染适配建议卡片
   */
  const renderSuggestionCard = (suggestion: ThemeAdaptationSuggestion) => (
    <Card className='border-primary/20 border-2'>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            {themeIcons[suggestion.suggestedTheme]}
            <CardTitle className='text-lg'>
              主题建议：{suggestion.suggestedTheme}
            </CardTitle>
          </div>
          <Badge variant='secondary' className='flex items-center gap-1'>
            {reasonIcons[suggestion.reason]}
            {suggestion.reason}
          </Badge>
        </div>
        <CardDescription>{suggestion.reasoning}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          {/* 置信度指示器 */}
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>置信度</span>
              <span>{Math.round(suggestion.confidence * 100)}%</span>
            </div>
            <Progress value={suggestion.confidence * 100} className='h-2' />
          </div>

          {/* 优先级指示器 */}
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>优先级</span>
              <span>{suggestion.priority}/10</span>
            </div>
            <Progress value={suggestion.priority * 10} className='h-2' />
          </div>

          {/* 操作按钮 */}
          <div className='flex gap-2 pt-2'>
            <Button
              onClick={() => actions.acceptSuggestion(suggestion)}
              className='flex-1'
              size='sm'
            >
              <CheckCircle className='mr-2 h-4 w-4' />
              接受
            </Button>
            <Button
              onClick={() => actions.rejectSuggestion(suggestion)}
              variant='outline'
              className='flex-1'
              size='sm'
            >
              <XCircle className='mr-2 h-4 w-4' />
              拒绝
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  /**
   * 渲染统计信息
   */
  const renderStatistics = () => (
    <div className='grid grid-cols-2 gap-4 md:grid-cols-4'>
      <Card>
        <CardContent className='p-4'>
          <div className='text-2xl font-bold'>
            {state.statistics.totalAdaptations}
          </div>
          <p className='text-muted-foreground text-xs'>总适配次数</p>
        </CardContent>
      </Card>
      <Card>
        <CardContent className='p-4'>
          <div className='text-2xl font-bold'>
            {state.statistics.acceptedAdaptations}
          </div>
          <p className='text-muted-foreground text-xs'>接受次数</p>
        </CardContent>
      </Card>
      <Card>
        <CardContent className='p-4'>
          <div className='text-2xl font-bold'>
            {state.statistics.rejectedAdaptations}
          </div>
          <p className='text-muted-foreground text-xs'>拒绝次数</p>
        </CardContent>
      </Card>
      <Card>
        <CardContent className='p-4'>
          <div className='text-2xl font-bold'>
            {Math.round(state.statistics.acceptanceRate * 100)}%
          </div>
          <p className='text-muted-foreground text-xs'>接受率</p>
        </CardContent>
      </Card>
    </div>
  );

  /**
   * 渲染适配历史
   */
  const renderAdaptationHistory = () => (
    <div className='space-y-3'>
      {state.adaptationHistory
        .slice(-5)
        .reverse()
        .map((suggestion, index) => (
          <Card key={suggestion.timestamp} className='p-3'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                {themeIcons[suggestion.suggestedTheme]}
                <span className='font-medium'>{suggestion.suggestedTheme}</span>
                <Badge variant='outline' className='text-xs'>
                  {suggestion.reason}
                </Badge>
              </div>
              <div className='text-muted-foreground text-sm'>
                {new Date(suggestion.timestamp).toLocaleTimeString()}
              </div>
            </div>
            <p className='text-muted-foreground mt-1 text-sm'>
              {suggestion.reasoning}
            </p>
          </Card>
        ))}
      {state.adaptationHistory.length === 0 && (
        <p className='text-muted-foreground py-8 text-center'>暂无适配历史</p>
      )}
    </div>
  );

  /**
   * 渲染用户行为数据
   */
  const renderUserBehavior = () => {
    if (!state.userBehavior) {
      return (
        <p className='text-muted-foreground py-8 text-center'>
          暂无用户行为数据
        </p>
      );
    }

    const { themeUsage, statistics } = state.userBehavior;

    return (
      <div className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          <Card>
            <CardContent className='p-4'>
              <div className='text-lg font-semibold'>
                {statistics.totalSessions}
              </div>
              <p className='text-muted-foreground text-xs'>总会话数</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className='p-4'>
              <div className='text-lg font-semibold'>
                {Math.round(statistics.averageSessionDuration / 1000 / 60)}分钟
              </div>
              <p className='text-muted-foreground text-xs'>平均会话时长</p>
            </CardContent>
          </Card>
        </div>

        <div className='space-y-3'>
          <h4 className='font-medium'>主题使用情况</h4>
          {Object.entries(themeUsage).map(([theme, usage]) => (
            <div
              key={theme}
              className='flex items-center justify-between rounded border p-3'
            >
              <div className='flex items-center gap-2'>
                {themeIcons[theme as SupportedTheme]}
                <span className='capitalize'>{theme}</span>
              </div>
              <div className='text-right'>
                <div className='font-medium'>{usage.sessionCount} 次</div>
                <div className='text-muted-foreground text-sm'>
                  {Math.round(usage.totalTime / 1000 / 60)} 分钟
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  /**
   * 渲染设置面板
   */
  const renderSettings = () => (
    <div className='space-y-6'>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div>
            <h4 className='font-medium'>自动适配</h4>
            <p className='text-muted-foreground text-sm'>
              启用智能主题自动适配功能
            </p>
          </div>
          <Switch
            checked={autoAdaptationEnabled}
            onCheckedChange={actions.toggleAutoAdaptation}
          />
        </div>

        <div className='flex items-center justify-between'>
          <div>
            <h4 className='font-medium'>学习功能</h4>
            <p className='text-muted-foreground text-sm'>
              记录和学习用户使用习惯
            </p>
          </div>
          <Switch
            checked={state.isLearning}
            onCheckedChange={actions.toggleLearning}
          />
        </div>
      </div>

      <div className='space-y-3'>
        <h4 className='font-medium'>置信度阈值</h4>
        <p className='text-muted-foreground text-sm'>
          自动接受建议的最低置信度：{confidenceThreshold}%
        </p>
        <Slider
          value={[confidenceThreshold]}
          onValueChange={(value) => {
            const newValue = value[0] ?? 75;
            setConfidenceThreshold(newValue);
            actions.setConfidenceThreshold(newValue / 100);
          }}
          max={100}
          min={50}
          step={5}
          className='w-full'
        />
      </div>

      <div className='space-y-3'>
        <h4 className='font-medium'>活动记录</h4>
        <div className='grid grid-cols-2 gap-2'>
          {(['reading', 'coding', 'browsing', 'media'] as UserActivity[]).map(
            (activity) => (
              <Button
                key={activity}
                variant='outline'
                size='sm'
                onClick={() => actions.recordActivity(activity)}
              >
                记录 {activity}
              </Button>
            )
          )}
        </div>
      </div>

      <div className='space-y-3'>
        <h4 className='font-medium'>数据管理</h4>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => {
              const data = actions.exportUserData();
              const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json',
              });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = 'smart-theme-data.json';
              a.click();
              URL.revokeObjectURL(url);
            }}
          >
            导出数据
          </Button>
          <Button variant='outline' size='sm' onClick={actions.clearAllData}>
            清除数据
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <TooltipProvider>
      <div className={className}>
        <div className='space-y-6'>
          {/* 错误提示 */}
          {state.error && (
            <Alert variant='destructive'>
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          {/* 当前建议 */}
          {state.currentSuggestion && (
            <div className='space-y-3'>
              <div className='flex items-center gap-2'>
                <Lightbulb className='text-primary h-5 w-5' />
                <h3 className='text-lg font-semibold'>智能建议</h3>
              </div>
              {renderSuggestionCard(state.currentSuggestion)}
            </div>
          )}

          {/* 快速操作 */}
          <div className='flex gap-2'>
            <Button
              onClick={actions.requestAdaptation}
              disabled={state.isAdapting}
              size='sm'
            >
              <TrendingUp className='mr-2 h-4 w-4' />
              {state.isAdapting ? '分析中...' : '获取建议'}
            </Button>

            <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
              <DialogTrigger asChild>
                <Button variant='outline' size='sm'>
                  <Settings className='mr-2 h-4 w-4' />
                  设置
                </Button>
              </DialogTrigger>
              <DialogContent className='max-w-md'>
                <DialogHeader>
                  <DialogTitle>智能主题设置</DialogTitle>
                  <DialogDescription>配置智能主题适配功能</DialogDescription>
                </DialogHeader>
                {renderSettings()}
              </DialogContent>
            </Dialog>
          </div>

          {/* 详细信息标签页 */}
          <Tabs defaultValue='statistics' className='w-full'>
            <TabsList className='grid w-full grid-cols-4'>
              <TabsTrigger value='statistics'>
                <BarChart3 className='mr-2 h-4 w-4' />
                统计
              </TabsTrigger>
              <TabsTrigger value='history'>
                <Clock className='mr-2 h-4 w-4' />
                历史
              </TabsTrigger>
              <TabsTrigger value='behavior'>
                <Activity className='mr-2 h-4 w-4' />
                行为
              </TabsTrigger>
              {showDebugInfo && (
                <TabsTrigger value='debug'>
                  <Info className='mr-2 h-4 w-4' />
                  调试
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value='statistics' className='space-y-4'>
              <h3 className='text-lg font-semibold'>适配统计</h3>
              {renderStatistics()}
            </TabsContent>

            <TabsContent value='history' className='space-y-4'>
              <h3 className='text-lg font-semibold'>适配历史</h3>
              {renderAdaptationHistory()}
            </TabsContent>

            <TabsContent value='behavior' className='space-y-4'>
              <h3 className='text-lg font-semibold'>用户行为</h3>
              {renderUserBehavior()}
            </TabsContent>

            {showDebugInfo && (
              <TabsContent value='debug' className='space-y-4'>
                <h3 className='text-lg font-semibold'>调试信息</h3>
                <pre className='bg-muted max-h-96 overflow-auto rounded p-4 text-xs'>
                  {JSON.stringify(actions.getDebugInfo(), null, 2)}
                </pre>
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>
    </TooltipProvider>
  );
}
